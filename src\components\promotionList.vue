<template>
  <view>
    <template v-if="status === 1 || status === 2 || status === 3 || status === 18">
      <view
        class="bg-white mb_20 p-20 radius15"
        v-for="(item, index) in list"
        :key="index"
      >
        <view class="f fac fjsb mb_30">
          <view>订单号: {{ item.order.order_sn }}</view>
          <view class="c-f1">{{ item.order.status | formatOrderStatus }}</view>
        </view>
        <!-- 商品部分 -->
        <view
          class="f fac fjsb"
          v-for="goods in item.order.order_items"
          :key="goods.id"
        >
          <view class="f fac">
            <u--image
              radius="20rpx"
              :showLoading="true"
              width="160rpx"
              height="160rpx"
              :src="goods.image_url"
            ></u--image>
            <view class="ml_20">
              <view class="ell font_size16">{{ goods.title }}</view>
              <view class="c-8a mt_10">规格: {{ goods.sku_title }}</view>
            </view>
          </view>
          <view>
            <view style="text-align: right">X {{ goods.qty }}</view>
            <view>￥{{ toYuan(goods.amount) }}</view>
          </view>
        </view>
        <view style="text-align: right" class="mt_10 mb_20">
          共{{ item.order.order_items.length }}件商品 实付:￥{{
            toYuan(item.order.amount)
          }}
        </view>
        <u-line></u-line>
        <view class="mt_20 c-gray4">
          下单会员:
          {{
            item.order.user.nickname
              ? item.order.user.nickname
              : item.order.user.username
          }}({{ item.order.user.id }})
        </view>
        <view class="f fac mt_10 c-gray4">
          分成金额: ￥{{ toYuan(item.amount) }}
          <view class="settle-status ml_20">
            {{ item.status_name }}
          </view>
        </view>
      </view>
    </template>
    <template v-if="status === 14">
        <view
        class="bg-white mb_20 p-20 radius15"
        v-for="(item, index) in list"
        :key="index"
      >
        <view class="f fac fjsb mb_30">
          <view>订单号: {{ item.order_sn }}</view>
          <view class="c-f1">{{ item.flow_point | formatFlowPoint }}</view>
        </view>
        <!-- 商品部分 -->
        <view
          class="f fac fjsb"
        >
          <view class="f fac">
            <u--image
              radius="20rpx"
              :showLoading="true"
              width="160rpx"
              height="160rpx"
              :src="item.product_img"
            ></u--image>
            <view class="ml_20">
              <view class="ell font_size16">{{ item.product_name }}</view>
            </view>
          </view>
          <view>
            <view style="text-align: right">X1</view>
            <view>￥{{ toYuan(item.total_pay_amount) }}</view>
          </view>
        </view>
        <view style="text-align: right" class="mt_10 mb_20">
          共1件商品 实付:￥{{
            toYuan(item.total_pay_amount)
          }}
        </view>
        <u-line></u-line>
        <view class="mt_20 c-gray4">
          下单会员:
          {{
            item.user.nickname
              ? item.user.nickname
              : item.user.username
          }}({{ item.user.id }})
        </view>
        <view class="f fac mt_10 c-gray4">
          分成金额: ￥{{ toYuan(item.award_amount) }}
          <view class="settle-status ml_20">
            {{ item.status | formatStatus }}
          </view>
        </view>
      </view>
    </template>
    <template v-if="status === 17">
        <view
        class="bg-white mb_20 p-20 radius15"
        v-for="(item, index) in list"
        :key="index"
      >
        <view class="f fac fjsb mb_30">
          <view>订单号: {{ item.small_shop_order.order_sn }}</view>
          <view class="c-f1">{{ item.small_shop_order.status | formatOrderStatus }}</view>
        </view>
        <!-- 商品部分 -->
        <view
          class="f fac fjsb"
          v-for="goods in item.small_shop_order.small_shop_order_items"
          :key="goods.id"
        >
          <view class="f fac">
            <u--image
              radius="20rpx"
              :showLoading="true"
              width="160rpx"
              height="160rpx"
              :src="goods.image_url"
            ></u--image>
            <view class="ml_20">
              <view class="ell font_size16">{{ goods.title }}</view>
              <view class="c-8a mt_10">规格: {{ goods.sku_title }}</view>
            </view>
          </view>
          <view>
            <view style="text-align: right">X {{ goods.qty }}</view>
            <view>￥{{ toYuan(goods.amount) }}</view>
          </view>
        </view>
        <view style="text-align: right" class="mt_10 mb_20">
          共{{ item.small_shop_order.small_shop_order_items.length }}件商品 实付:￥{{
            toYuan(item.small_shop_order.amount)
          }}
        </view>
        <u-line></u-line>
        <view class="mt_20 c-gray4">
          下单会员:
          {{
            item.shop_user_info.nickname
              ? item.shop_user_info.nickname
              : item.shop_user_info.username
          }}({{ item.shop_user_info.id }})
        </view>
        <view class="f fac fjsb">
          <view class="f fac mt_10 c-gray4">
            分成金额: ￥{{ toYuan(item.amount) }}
            <view class="settle-status ml_20">
              {{ item.status | formatStatus }}
            </view>
          </view>
          <view>
            是否分账：
            <text v-if="item.is_split === 1">是</text>
            <text v-else>否</text>
          </view>
        </view>
        <view>
          <view class="f fac fjsb mt_10">
            <view>结算期：{{ item.settle_days }}天</view>
            <view v-if='item.status !== 0'>结算时间：{{ formatDateTime(item.settle_at) }}</view>
          </view>
          <view class="f fac fjsb mt_10">
            <view>分账金额：
              {{ toYuan(item.amount) }}
            </view>
            <text v-if="item.status === 2 && item.split_status === 1" >{{ toYuan(item.amount) }}</text>
            <text>-</text>
          </view>
          <view class="f fac fjsb mt_10">
            <view >收入金额：
              {{ toYuan(item.amount) }}
            </view>
            <view>
              <text v-if="item.status === 1" >{{ toYuan(item.amount) }}</text>
              <text>-</text>
            </view>
          </view>
        </view>
      </view>
    </template>
    <template v-if="status === 20">
        <view
        class="bg-white mb_20 p-20 radius15"
        v-for="(item, index) in list"
        :key="index"
      >
        <view class="f fac fjsb mb_30">
          <view>订单号: {{ item.order_sn }}</view>
          <view class="c-f1">{{ item.status | formatOrderStatus }}</view>
        </view>
        <!-- 商品部分 -->
        <view
          class="f fac fjsb"
        >
          <view class="f fac">
            <!-- <u--image
              radius="20rpx"
              :showLoading="true"
              width="160rpx"
              height="160rpx"
              :src="item.product_img"
            ></u--image> -->
            <view>
              <view class="ell font_size16">{{ item.title }}</view>
            </view>
          </view>
          <view>
            <view style="text-align: right">X1</view>
            <view>￥{{ toYuan(item.price) }}</view>
          </view>
        </view>
        <view style="text-align: right" class="mt_10 mb_20">
          共1件商品 实付:￥{{
            toYuan(item.price)
          }}
        </view>
        <u-line></u-line>
        <view class="mt_20 c-gray4">
          下单会员:
          {{
            item.user.nickname
              ? item.user.nickname
              : item.user.username
          }}({{ item.user.id }})
        </view>
        <view class="f fac mt_10 c-gray4">
          分成金额: ￥{{ toYuan(item.user_income_details.amount) }}
          <view class="settle-status ml_20">
            {{ item.status_name | formatStatus }}
          </view>
        </view>
      </view>
    </template>
    <template v-if="status === 23">
        <view
        class="bg-white mb_20 p-20 radius15"
        v-for="(item, index) in list"
        :key="index"
      >
        <view class="f fac fjsb mb_30">
          <view>订单号: {{ item.order_sn }}</view>
          <view class="c-f1">{{ item.order_status | formatOrderStatus }}</view>
        </view>
        <!-- 商品部分 -->
        <view
          class="f fac fjsb"
        >
          <view class="f fac">
            <u--image
              radius="20rpx"
              :showLoading="true"
              width="160rpx"
              height="160rpx"
              :src="item.movie_pic"
            ></u--image>
            <view class="ml_20">
              <view class="ell font_size16">{{ item.movie_title }}</view>
            </view>
          </view>
          <view>
            <view style="text-align: right">X{{item.quantity}}</view>
            <view>￥{{ toYuan(item.accounts_payable) }}</view>
          </view>
        </view>
        <view style="text-align: right" class="mt_10 mb_20">
          共{{ item.movie_title }}件商品 实付:￥{{
            toYuan(item.total_settle_amount)
          }}
        </view>
        <u-line></u-line>
        <view class="mt_20 c-gray4">
          下单会员:
          {{
            item.user.nickname
              ? item.user.nickname
              : item.user.username
          }}({{ item.user.id }})
        </view>
        <view class="f fac mt_10 c-gray4">
          分成金额: ￥{{ toYuan(item.user_income_details.amount) }}
          <view class="settle-status ml_20">
            {{ item.status_name }}
          </view>
        </view>
      </view>
    </template>
    <template v-if="status === 38">
        <view
        class="bg-white mb_20 p-20 radius15"
        v-for="(item, index) in list"
        :key="index"
      >
        <!-- <view class="f fac fjsb mb_30">
          <view>订单号: {{ item.order_sn }}</view>
          <view class="c-f1">{{ item.order_status | formatOrderStatus }}</view>
        </view> -->
        <!-- 商品部分 -->
        <!-- <view
          class="f fac fjsb"
        >
          <view class="f fac">
            <u--image
              radius="20rpx"
              :showLoading="true"
              width="160rpx"
              height="160rpx"
              :src="item.movie_pic"
            ></u--image>
            <view class="ml_20">
              <view class="ell font_size16">{{ item.movie_title }}</view>
            </view>
          </view>
          <view>
            <view style="text-align: right">X{{item.quantity}}</view>
            <view>￥{{ toYuan(item.accounts_payable) }}</view>
          </view>
        </view>
        <view style="text-align: right" class="mt_10 mb_20">
          共{{ item.movie_title }}件商品 实付:￥{{
            toYuan(item.total_settle_amount)
          }}
        </view>
        <u-line></u-line> -->
        <view class="mt_20 c-gray4">
          下单会员:
          {{
            item.user.nickname
              ? item.user.nickname
              : item.user.username
          }}({{ item.user.id }})
        </view>
        <view class="f fac mt_10 c-gray4">
          分成金额: ￥{{ toYuan(item.amount) }}
          <view class="settle-status ml_20">
            {{ item.status_name }}
          </view>
        </view>
      </view>
    </template>
    <template v-if="status === 44 || status === 45">
        <view
        class="bg-white mb_20 p-20 radius15"
        v-for="(item, index) in list"
        :key="index"
      >
        <view class="f fac fjsb mb_30">
          <view>订单号: {{ item.order_sn }}</view>
          <view class="c-f1">{{ item.order_info.status_name }}</view>
        </view>
        <!-- 商品部分 -->
        <view
          class="f fac fjsb"
        >
          <view class="f fac">
            <u--image
              radius="20rpx"
              :showLoading="true"
              width="160rpx"
              height="160rpx"
              :src="item.order_info.order_items[0].image_url"
            ></u--image>
            <view class="ml_20">
              <view class="ell font_size16">{{ item.order_info.order_items[0].title }}</view>
            </view>
          </view>
          <view>
            <view style="text-align: right">X{{item.order_info.order_items[0].qty}}</view>
            <view>￥{{ toYuan(item.order_info.order_items[0].amount) }}</view>
          </view>
        </view>
        <view style="text-align: right" class="mt_10 mb_20">
          共{{ item.order_info.order_items[0].qty }}件商品 实付:￥{{
            toYuan(item.order_info.order_items[0].payment_amount)
          }}
        </view>
        <u-line></u-line>
        <view class="mt_20 c-gray4">
          下单会员:
          {{
            item.order_info.user.nickname
              ? item.order_info.user.nickname
              : item.order_info.user.username
          }}({{ item.order_info.user.id }})
        </view>
        <view class="f fac mt_10 c-gray4">
          <view v-if="status === 44">分成金额: ￥{{ toYuan(item.brand_settle_amount) }}</view>
          <view v-if="status === 45">分成金额: ￥{{ toYuan(item.store_settle_amount) }}</view>
          <view class="settle-status ml_20">
            {{ item.settle_status_name }}
          </view>
        </view>
      </view>
    </template>
    <u-loadmore :status="loadStatus" fontSize="24" />
  </view>
</template>
<script>
export default {
    computed:{
        loadStatus(){
            return this.isLast ? "没有更多了" : "加载更多"
        }
    },
  props: {
    isLast:{
        type: Boolean
    },
    /**
     * 1 = 区域分红
     * 2 = 分销分成
     * 3 = 招商分红
     * 4 = cps分成 14
     * 5 = 小商店分成 17
     * 6 = 课程分红 18
     * 7 = 聚推联盟分成 20
     * 8 = 电影票分红 23
     * 9 = 美团分销分成 38
     * 9 = 本地生活品牌分成 44
     * 9 = 本地生活门店分成 45
     */
    status: {
      type: Number,
      default: () => {
        return null
      },
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  filters: {
    // cps分成订单状态
    formatFlowPoint(status){
        let name = ""
        switch(status){
            case "PAY_SUCC":
                name = "支付完成"
            break;
            case "REFUND":
                name = "退款"
            break;
            case "SETTLE":
                name = "结算"
            break;
            case "CONFIRM":
                name = "确认收货"
            break;
        }
        return name;
    },
    // 格式化奖励状态
    formatStatus(status) {
      
      let name = ''
      switch (status) {
        case 0:
          name = '未结算'
          break
        case 1:
          name = '已结算'
          break
        case -1:
          name = '失效'
        case 2:
          name = '子商户'
          break
      }
      return name;
    },
    // 格式化订单状态
    formatOrderStatus(status) {
      let name = ''
      switch (status) {
        case 0:
          name = '待付款'
          break
        case 1:
          name = '待发货'
          break
        case 2:
          name = '待收货'
          break
        case 3:
          name = '已完成'
          break
        case -1:
          name = '已关闭'
          break
      }
      return name
    },
  },
}
</script>
<style lang="scss" scoped>
.settle-status {
  padding: 5rpx 10rpx;
  color: #f96d20;
  background-color: rgba($color: #f96d20, $alpha: 0.1);
  border-radius: 8rpx;
}
</style>
