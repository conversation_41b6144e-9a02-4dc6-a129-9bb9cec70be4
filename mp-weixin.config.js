// 微信小程序专用优化配置
module.exports = {
  // 小程序特定的优化配置
  optimization: {
    // 移除未使用的代码
    removeUnusedCode: true,
    
    // 压缩配置
    compress: {
      // 移除console
      drop_console: true,
      drop_debugger: true,
      // 移除未使用的变量
      unused: true,
      // 移除死代码
      dead_code: true
    },
    
    // 代码分割策略
    splitChunks: {
      // 只对异步chunk进行分割
      chunks: 'async',
      // 最小chunk大小
      minSize: 20000,
      // 最大chunk大小
      maxSize: 244000,
      cacheGroups: {
        // 第三方库
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10
        },
        // 公共代码
        common: {
          minChunks: 2,
          chunks: 'all',
          name: 'common',
          priority: 5
        }
      }
    }
  },
  
  // 排除的文件和模块
  excludes: [
    // PC适配文件
    'static/adapt-pc/**',
    // H5特定文件
    'template.h5.html',
    // 大型图标字体（改为按需引入）
    'style/css/iconfont.css'
  ],
  
  // 按需加载的模块
  lazyModules: [
    'xgplayer',
    'xgplayer-hls',
    'vue-quill-editor',
    '@tencentcloud/chat'
  ],
  
  // 资源优化
  assets: {
    // 图片压缩
    imageCompression: true,
    // 字体子集化
    fontSubset: true,
    // CSS压缩
    cssMinify: true
  }
}
