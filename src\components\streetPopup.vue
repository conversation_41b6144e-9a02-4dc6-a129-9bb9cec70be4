<template>
	<view>
		<view class="b-mask" :class="streetObj.show?'true': ''" @tap.stop="_closeDateLw"></view>
		<!-- 选择街道 -->
		<view v-show="streetObj.show" class="dateBe" :class="streetObj.show?'true':''">
			<view class="head">
				<view class="ll" @tap.stop="_closeStreet">取消</view>
				<view>选择街道</view>
				<view class="rr" @tap.stop="confirmAdd">确定</view>
			</view>
			<view class="main">
				<!-- @change="bindPickerChange" -->
				<picker-view
					v-if="streetData.length > 0"
					indicator-style="height: 50px;" 
					style="width: 100%; height: 300px;" 
					:value="pickerstreetVal"
					@change="bindPickerChange"
					@pickend="bindPickend"
				 >
					<picker-view-column id="streetData">
						<view v-for="(item,index) in streetData" :key="index" style="line-height: 50px">{{item.name}}</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"streetPopup",
		props:{
			streetObj:{
				type:Object,
				default:{}
			},
		},
		data() {
			return {
				pickerstreetVal:[0],
				streetData:[],
				street:{
					town:'',
					town_id:0
				}
			};
		},
		created() {
			this.$nextTick(function(){
				this.streetListData(this.streetObj.countyId);
			})
		},
		onShow() {
		},
		mounted() {
		},
		methods:{
			confirmAdd() {
				let _json = this.streetData;
				this.street.town = _json[this.pickerstreetVal].name;
				this.street.town_id = _json[this.pickerstreetVal].id;
				this.$emit('streetConfirm', this.street);
				this._closeStreet();
			},
			bindPickerChange(e) {
				let val = e.detail.value;
				this.pickerstreetVal[0] = val[0];
			},
			_closeStreet() {
				// this.streetShow = false;
				this.$emit('closeStreet', this.streetObj.show);
				this.pickerstreetVal = [0];
				this.streetData = []
			},
			_closeDateLw() {
				this.$emit('closeStreet', this.streetObj.show);
			},
			streetListData(id) {
				this.get('/api/region/list?parent_id=' + id, {}, true).then((res) => {
					if(res.code === 0) {
						let data = res.data;
						this.streetData = data.list;
								
					} else {
						this.toast(res.msg);
					}
				}).catch((Error) => {
					console.log(Error);
				})
			},
		}
	}
</script>

<style>
	/*选择地区*/
	/*地址选择器*/
	/*--------------------------------*/
	.btn-area {
		padding: 0rpx 20rpx;
	}
	
	.dateBe {
		position: fixed;
		bottom: 0rpx;
		left: -5rpx;
		width: 760rpx;
		height:800rpx;
		padding: 0rpx 5rpx;
		box-sizing: border-box;
		z-index: 11000;
		font-size: 28rpx;
		border-top: 1rpx solid #d9d9d9;
		opacity: 0;
		transform: translate(-750rpx, 0rpx);
	}
	
	.dateBe.true {
		opacity: 1;
		transform: translate(0rpx, 0rpx);
	}
	
	.dateBe .head {
		display: flex;
		flex-flow: nowrap;
		padding: 0rpx 30rpx;
		line-height: 80rpx;
		border-bottom: 1rpx solid #d9d9d9;
		background: #f8f8f8;
	}
	.main {
		height: 900rpx;
	}
	.dateBe .head .ll {
		flex: 1;
	}
	
	.dateBe .head .rr {
		text-align: right;
		flex: 1;
	}
	
	.dateBe .main {
		background: #fff;
	}
	
	.dateBe .main view {
		text-align: center;
	}
</style>
