<template>
    <view>
        <view class="main-box mt_20">
            <view class="black fwb">订单</view>
            <view class="f fac fjsb" v-for="item in order_details" :key="item.order_sn">
                <view class="black mt_30">{{ item.order_sn }}</view>
                <view class="yuan">¥{{ toYuan(item.amount) }}</view>
            </view>
            <view class="f fac fjsb mt_30">
                <view class="black">总计：</view>
                <view class="yuan">¥{{ toYuan(amount_total) }}</view>
            </view>
        </view>
        <view class="main-box mt_20">
            <view class="black fwb mb_30">打款凭证</view>
            <view class="f fac fw">
                <u--image
                    v-for='item in payment_vouchers'
                    :key='item.src'
                    class="mb_24 mr_15"
                    :src="item.src"
                    radius="10rpx"
                    width="202rpx"
                    height="202rpx"
                ></u--image>
            </view>
        </view>
        <view class="main-box mt_20" v-if="confirm_status == 2">
            <view class="black fwb mb_30">驳回理由</view>
            <view class="f fac fw">
            </view>
        </view>
        <!-- 确认收款提示 -->
        <u-modal :show="isShow" showCancelButton @cancel='isShow = false' @confirm='confirm'>
            <p class="fs_30">是否确认收款 ！</p>
        </u-modal>
    </view>
</template>
<script>
export default {
    name: 'payVoucher',
    data() {
        return {
            rejectedShow: false,
            value1: '', // 驳回理由
            id: null,
            confirm_status: null,

            reject_reason: '', // 驳回理由
            payment_vouchers: [], // 打款凭证
            order_details: [], // 订单信息
            amount_total: 0, // 总计

            isShow: false, // 确认收款弹窗
        }
    },
    onLoad(options) {
        this.id = options.id;
        this.paymentLogInfo();
    },
    methods: {
        async paymentLogInfo() {
            let res = await this.post('/api/agent/reward/pay/paymentLogInfo',{id: parseInt(this.id)});
            if (res.code === 0) {
                this.amount_total = res.data.pay_total_amount;
                this.order_details = res.data.order_details;
                this.payment_vouchers = res.data.payment_vouchers;
                this.reject_reason = res.data.reject_reason;
                this.confirm_status = res.data.confirm_status
            }
        },
    }
}
</script>
<style lang="scss" scoped>
.main-box {
    width: 702rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-left: 24rpx;
    padding-left: 24rpx;
    box-sizing: border-box;
    padding-right: 24rpx;
    padding-bottom: 30rpx;
    padding-top: 24rpx;
}
.yuan {
    font-weight: 500;
    font-size: 26rpx;
    color: #F14E4E;
}
.black {
    font-weight: 400;
    font-size: 26rpx;
    color: #202020;
}
.red {
    font-weight: 500;
    font-size: 28rpx;
    color: #F15353;
}
.fwb {
    font-weight: bold;
    font-size: 28rpx;
}
.button-box {
    width: 750rpx;
    height: 98rpx;
    background: #FFFFFF;
    position: fixed;
    bottom: 0rpx;
}
.fw {
    flex-wrap: wrap;
}
::v-deep .u-button--info {
    width: 180rpx;
    height: 72rpx;
    color: #f14e4e;
    border-color: #f14e4e;
}
</style>