<template>
  <view>
    <view class="top-box">
      <view class="title-box f fac">
        <view class="avatar-box">
          <u--image
            :src="info.avatar"
            shape="circle"
            width="128rpx"
            height="128rpx"
          ></u--image>
        </view>
        <view class="name-box">
          <view class="name">{{ info.username }}</view>
          <view class="mt_20 grey">{{ info.level_name }}</view>
        </view>
      </view>
    </view>
    <view class="main-box mt_50 f fac fjsa">
      <view class="f fdc fac">
        <view class="title-num">{{ toYuan(info.reward_amount) }}</view>
        <view class="mt_15 title-grey">已获得平级奖励</view>
      </view>
      <view class="f fdc fac">
        <view class="title-num">{{ toYuan(info.kpi_amount) }}</view>
        <view class="mt_15 title-grey">业绩统计</view>
      </view>
    </view>
    <view class="main-boxs mt_20">
      <u-grid :border="false" col="4">
        <u-grid-item
          v-for="(item, index) in agentList"
          :key="index"
          @click="navTo(item.url)"
        >
          <view class="iconfont black" :class="item.nameClass"></view>
          <text class="grid-text">{{ item.title }}</text>
        </u-grid-item>
      </u-grid>
    </view>
    <myTabBar ref="myTabBar"></myTabBar>
  </view>
</template>

<script>
import myTabBar from '../../components/tabbar.vue'
export default {
  name: 'agentHome',
  components: {
    myTabBar,
  },
  data() {
    return {
      agentList: [
        {
          nameClass: 'icon-caigoudan',
          title: '采购单',
          url: '/packageE/agent/agentOrder',
        },
        {
          nameClass: 'icon-pingjijiang',
          title: '平级奖',
          url: '/packageE/agent/levelAward',
        },
        {
          nameClass: 'icon-shuju',
          title: '业绩统计',
          url: '/packageE/agent/performanceStats',
        },
        {
          nameClass: 'icon-zhituidailishang',
          title: '直推代理商',
          url: '/packageE/agent/pushAgent',
        },
        {
          nameClass: 'icon-pingjijiangdakuan',
          title: '平级奖打款',
          url: '/packageE/agent/levelPayment',
        },
        {
          nameClass: 'icon-pingjijiangqueren',
          title: '平级奖确认',
          url: '/packageE/agent/levelConfirm',
        },
        {
          nameClass: 'icon-dakuanjilu',
          title: '打款记录',
          url: '/packageE/agent/paymentRecords',
        },
        {
          nameClass: 'icon-caigouyue',
          title: '采购余额',
          url: '/packageE/agent/balancePurchase',
        },
      ],
      info: {
        avatar: '',
        kpi_amount: '',
        reward_amount: '',
        level_name: '',
        nickname: '',
        username: '',
      },
    }
  },
  onShow() {
    this.getAgentSetting()
    this.getAgentPage()
    this.$nextTick(() => {
      this.$refs.myTabBar.tabBarActive = -1
    })
  },
  methods: {
    // 获取代理商数据
    async getAgentPage() {
      const res = await this.post('/api/agent/page')
      if (res.code === 0) {
        this.info = res.data
      }
    },
    // 获取代理商基础设置
    async getAgentSetting() {
      const res = await this.get('/api/agent/setting/get')
      if (res.code === 0) {
        if (
          res.data.setting.value.base_setting.custom_purchase_order_name !== ''
        ) {
          this.agentList[0].title =
            res.data.setting.value.base_setting.custom_purchase_order_name
        }
        if (res.data.setting.value.base_setting.custom_reward_name !== '') {
          this.agentList[1].title =
            res.data.setting.value.base_setting.custom_reward_name
        }
        if (res.data.setting.value.base_setting.custom_agent_name !== '') {
          this.agentList[3].title =
            '直推' + res.data.setting.value.base_setting.custom_agent_name
        }
        if (res.data.setting.value.base_setting.custom_reward_name !== '') {
          this.agentList[4].title =
            res.data.setting.value.base_setting.custom_reward_name + '打款'
        }
        if (res.data.setting.value.base_setting.custom_reward_name !== '') {
          this.agentList[5].title =
            res.data.setting.value.base_setting.custom_reward_name + '确认'
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.top-box {
  height: 190rpx;
  background: linear-gradient(180deg, #f15353 0%, #f5f5f5 100%);
  border-radius: 0rpx;
  padding-top: 37rpx;
  padding-left: 24rpx;
  box-sizing: border-box;
  .title-box {
    width: 702rpx;
    height: 188rpx;
    background: #ffffff;
    border-radius: 16rpx;
    .avatar-box {
      margin-left: 30rpx;
      width: 128rpx;
      height: 128rpx;
    }
    .name-box {
      margin-left: 16rpx;
    }
  }
}
.main-box {
  width: 702rpx;
  min-height: 141rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-left: 24rpx;
}
.main-boxs {
  width: 702rpx;
  min-height: 141rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-left: 24rpx;
  padding-top: 39rpx;
  padding-bottom: 28rpx;
  padding-left: 29rpx;
  padding-right: 43rpx;
  box-sizing: border-box;
}
.grid-text {
  font-size: 24rpx;
  color: #202020;
  padding: 20rpx 0 30rpx 0rpx;
  box-sizing: border-box;
}
.name {
  font-weight: normal;
  font-size: 32rpx;
  color: #202020;
}
.grey {
  font-weight: 400;
  font-size: 26rpx;
  color: #919191;
}
.fjsa {
  justify-content: space-around;
}
.fdc {
  flex-direction: column;
}
.title-num {
  font-size: 30rpx;
  color: #202020;
}
.title-grey {
  font-size: 24rpx;
  color: #919191;
}
.black {
  color: #202020;
}
</style>
