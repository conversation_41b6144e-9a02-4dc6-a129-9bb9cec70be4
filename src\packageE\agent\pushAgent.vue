<template>
  <view>
    <view class="search-box pt_15 f fac">
      <view class="ml_25" style="width: 500rpx">
        <u-search
          height="64rpx"
          placeholder="请输入会员名称/手机号"
          clearabled
          :showAction="false"
          searchIconSize="40"
          v-model="keyword"
          @search="changeSearch"
        ></u-search>
      </view>
      <view class="f fac ml_30" @click="chooseLevel">
        <view class="search-title">{{ level_name }}</view>
        <view class="ml_10 mt_5">
          <u-icon
            v-if="!levelShow"
            name="arrow-down"
            color="#AAAAB3"
            size="28"
          ></u-icon>
          <u-icon
            v-if="levelShow"
            name="arrow-up"
            color="#AAAAB3"
            size="28"
          ></u-icon>
        </view>
      </view>
    </view>
    <view class="main-box mt_20" v-for="item in agentList" :key="item.id">
      <view class="f fjsb">
        <view class="f mt_25 ml_25">
          <u--image
            :src="item.user.avatar"
            shape="circle"
            width="96rpx"
            height="96rpx"
          ></u--image>
          <view class="ml_15">
            <view class="f fac">
              <view class="name">{{ item.user.nickname }}</view>
            </view>
            <view class="mt_25 grey">{{ item.user.username }}</view>
          </view>
        </view>
        <view class="agent_box ml_10">
          {{ item.agent_level.level_name }}
        </view>
        <!-- <view class="mt_25 mr_25">
          <text class="orage">待审核</text>
          <text class="green">已通过</text>
          <text class="red">已驳回</text>
        </view> -->
      </view>
      <view class="nick-box f fac fjsb">
        <view class="">
          上级：【{{ item.user.parent_user.id }}】{{
            item.user.parent_user.nickname
          }}
        </view>
        <view class="">
          {{ item.user.parent_user.agent.agent_level.level_name }}
        </view>
      </view>
      <view class="caigou-box f fac fjsa">
        <view class="f flc fac">
          <view class="name">{{ toYuan(item.kpi_amount) }}</view>
          <view class="grey">个人业绩</view>
          <view class="f fac title" @click="navJump(item)">
            个人资料
            <u-icon
              class="ml_5"
              name="arrow-right"
              color="#F14E4E"
              size="20"
            ></u-icon>
          </view>
        </view>
        <view class="f flc fac">
          <view class="name">{{ item.order_count }}</view>
          <view class="grey">采购单数量</view>
          <view
            class="f fac title"
            @click="navTo('/packageE/agent/childrenOrder?id=' + item.user.id)"
          >
            采购单
            <u-icon
              class="ml_5"
              name="arrow-right"
              color="#F14E4E"
              size="20"
            ></u-icon>
          </view>
        </view>
      </view>
    </view>
    <view v-if="levelShow" class="level-box">
      <scroll-view style="height: 240rpx" scroll-y>
        <view v-for="item in levelList" :key="item.id">
          <view class="mt_20" @click="changeLevel(item)">
            {{ item.level_name }}
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'pushAgent',
  data() {
    return {
      levelList: [],
      levelShow: false,
      level_name: '选择等级',
      level_id: null,
      keyword: '',
      agentList: [], // 代理商列表
      page: 1,
      pageSize: 10,
      total: null,
    }
  },
  mounted() {
    this.getAgentLevelSelect()
    this.getAgentChildren()
  },
  methods: {
    // 代理商低等级选择
    async getAgentLevelSelect() {
      const res = await this.post('/api/agent/level/lowSelect')
      if (res.code === 0) {
        this.levelList = res.data
      }
    },
    // 打开选择等级
    chooseLevel() {
      this.levelShow = !this.levelShow
    },
    // 选择等级
    changeLevel(item) {
      this.level_id = item.id
      this.level_name = item.level_name
      this.levelShow = false
      this.page = 1
      this.getAgentChildren()
    },
    // 搜索
    changeSearch() {
      this.page = 1
      this.getAgentChildren()
    },
    // 代理商列表
    async getAgentChildren() {
      const data = {
        level_id: this.level_id,
        keyword: this.keyword,
        page: this.page,
        pageSize: this.pageSize,
      }
      const res = await this.post('/api/agent/children', data)
      if (res.code === 0) {
        this.agentList = res.data.list
        this.total = res.data.total
      }
    },
    // 页面跳转
    navJump(item) {
      this.navTo('/packageE/agent/agentPerson')
      uni.setStorageSync('agent_info', item)
    },
  },
}
</script>
<style lang="scss" scoped>
.search-box {
  width: 750rpx;
  height: 100rpx;
  background: #ffffff;
}
.search-title {
  font-weight: 400;
  font-size: 26rpx;
  color: #202020;
  margin-right: 20rpx;
}
.main-box {
  width: 702rpx;
  height: 400rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-left: 24rpx;
}
.agent_box {
  height: 36rpx;
  background: #ffffff;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-top: 30rpx;
  margin-right: 40rpx;
}
.name {
  font-weight: bold;
  font-size: 30rpx;
  color: #00001c;
}
.nick-box {
  width: 654rpx;
  height: 66rpx;
  background: #f6f6f6;
  border-radius: 16rpx;
  margin-left: 24rpx;
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666666;
  padding: 0 20rpx;
  box-sizing: border-box;
}
.caigou-box {
  width: 654rpx;
  height: 150rpx;
  background: #f6f6f6;
  border-radius: 16rpx;
  margin-left: 24rpx;
  margin-top: 20rpx;
}
.title {
  font-weight: normal;
  font-size: 24rpx;
  color: #f14e4e;
}
.grey {
  font-size: 26rpx;
  color: #666666;
}
.orage {
  color: #ff8a14;
}
.green {
  color: #19be76;
}
.red {
  color: #f14e4e;
}
.fjsa {
  justify-content: space-around;
}
.flc {
  flex-direction: column;
}
.level-box {
  padding: 20rpx;
  width: 200rpx;
  height: 300rpx;
  background-color: #ffffff;
  position: fixed;
  top: 100rpx;
  right: 20rpx;
  z-index: 9;
  border-radius: 10rpx;
  box-sizing: border-box;
  box-shadow: 5rpx 5rpx 5rpx 5rpx #dcdcdc;
}
</style>
