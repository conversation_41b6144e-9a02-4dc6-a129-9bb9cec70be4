<template>
    <view>
        <view class="tabs-box">
            <u-tabs 
                :list="tabList"
                :scrollable="false"
                lineWidth="30"
                lineHeight="5"
                lineColor="#F15353"
                :activeStyle="{
                    color: '#F15353',
                    fontWeight: 'bold',
                }"
                :inactiveStyle="{
                    color: '#606266',
                }"
                itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
                @click="tabsTap"
            >
            </u-tabs>
         </view>
        <template v-for="(item,index) in list">
            <view class="main-box mt_20 pl_25 pr_25" :key="item.id">
               <view class="f fac fjsb pt_30">
                   <view class="black f fac">
                       <view v-if="tabName === '待确认'">
                           <u-checkbox-group placement="column">
                               <u-checkbox
                                    activeColor="#F15353" 
                                    size="30" 
                                    shape="circle"
                                    :name="item.id"
                                    :checked="item.checked"
                                    @change='changeChecked(item,index)'
                                ></u-checkbox>
                           </u-checkbox-group>
                       </view>
                    <view class="">订单号：{{ item.order_sn }}</view>
                   </view>
                   <view class="red f fac" @click="navTo('/packageE/agent/checkVoucher?id=' + item.id + '&type=' + item.confirm_status)">
                       <view class="mr_10">￥{{ toYuan(item.amount) }}</view>
                       <view><u-icon name="arrow-right" color="#919191" size="20"></u-icon></view>
                   </view>
               </view>
               <view class="f fac fjsb mt_20 grey">
                   <view>{{ formatDateTime(item.created_at) }}</view>
                   <view v-if="tabName == '待确认'">待确认</view>
                   <view v-else-if="tabName == '已确认'">已确认</view>
                   <view v-else>已驳回</view>
               </view>
            </view>
        </template>
        
         <view class="mb_150"></view>
         <view class="button-box f fac fjsb" v-if="tabName == '待确认'">
            <view class="f fac ml_30">
                <u-checkbox-group v-model="allCheckbox">
                    <u-checkbox 
                        activeColor="#F15353" 
                        :checked="allFlag == 0 ? false : true" 
                        size="32" 
                        shape="circle" 
                        @change="checkAll"
                    ></u-checkbox>
                </u-checkbox-group>
                <view>
                    <text class="black">全选</text>
                    <text class="red">￥{{ toYuan(amount_total) }}</text>
                </view>
            </view>
            <view class="f fac mr_25">
                <view class="mr_25" style="width: 180rpx">
                    <u-button style="width: 180rpx !important;" :plain="true" type="error" shape="circle" text="驳回" @click="rejectedBut"></u-button>
                </view>
                <view>
                    <u-button style="width: 180rpx !important;" shape="circle" color="#F14E4E" text="确认收款" @click="remit"></u-button>
                </view>
            </view>
         </view>
         <view class="kefu-box f fac flc" @click="show = true">
            <view class="iconfont icon-linedesign-20 fs40 mt_5"></view>
            <view>客服</view>
         </view>
         <u-popup :show="show" mode="center" round="24rpx" @close="show = false">
            <view class="popup-box f fac flc fjsa">
                <view class="kefu">联系客服</view>
                <view>
                    <u--image
                        :src="image_url"
                        width="240rpx"
                        height="240rpx"
                    ></u--image>
                </view>
                <view class="black">电话：{{ phone }}</view>
            </view>
            <view class="icon-box" @click="show = false"><u-icon name="close-circle" color="#D6D6DC" size="64"></u-icon></view>
        </u-popup>
        <u-modal :show="rejectedShow" title="驳回理由" width="500rpx" @confirm="rejectedFun" @cancel="rejectedShow = false;value1 = ''" confirmColor="#F14E4E" confirmText="驳回" :showCancelButton="true">
            <u--textarea height="187rpx" width="452rpx" v-model="value1" placeholder="请输入" ></u--textarea>
        </u-modal>
        <!-- 确认收款提示 -->
        <u-modal :show="isShow" showCancelButton @cancel='isShow = false' @confirm='confirm'>
            <p class="fs_30">是否确认收款 ！</p>
        </u-modal>
    </view>
</template>
<script>
export default {
    name: 'levelConfirm',
    data() {
        return {
            checked:[],
            show:false,
            rejectedShow: false,
            tabList: [
                {
                    name: '待确认',
                }, 
                {
                    name: '已确认',
                }, 
                {
                    name: '已驳回'
                }
            ],
            tabName: '待确认',
            list: [],
            page: 1,
            pageSize: 10,
            total: 0,

            image_url: '',
            phone: '',
            value1: '', // 驳回理由
            allFlag: 0,
            allCheckbox: [], // 全选
            amount_total: 0,

            isShow: false,
        }
    },
    onLoad() {
        this.getList();
        this.getContact();
    },
    // 下拉刷新
    onReachBottom() {
        if (this.list.length < this.total) {
            this.page = this.page + 1;
            this.getList();
        }
    },
    methods: {
        remit() {
            let arr = this.list.filter(item => item.checked);
            if (arr.length > 0) {
                this.isShow = true;
            } else {
                this.showText('需勾选待打款的记录');
            }
        },
        rejectedBut() {
            let arr = this.list.filter(item => item.checked);
            if (arr.length > 0) {
                this.rejectedShow = true;
            } else {
                this.showText('需勾选待打款的记录');
            }
        },
        // 确认收款
        async confirm() {
            let ids = [];
            let arr = this.list.filter(item => item.checked);
            arr.forEach(element => {
                ids.push(parseInt(element.id));
            });
            let params = { ids };
            let res = await this.post('/api/agent/reward/receive/confirm',params);
            if (res.code === 0) {
                this.showText(res.msg);
                this.isShow = false;
                this.page = 1;
                this.list = [];
                this.total = 0;
                this.allFlag = 0;
                this.amount_total = 0;
                this.getList();
            }
        },
        // 驳回
        async rejectedFun() {
            let ids = [];
            let arr = this.list.filter(item => item.checked);
            arr.forEach(element => {
                ids.push(parseInt(element.id));
            });
            let params = { ids, reject_reason: this.value1 }
            let res = await this.post('/api/agent/reward/receive/reject',params);
            if (res.code === 0) {
                this.showText(res.msg);
                this.isShow = false;
                this.page = 1;
                this.list = [];
                this.total = 0;
                this.allFlag = 0;
                this.amount_total = 0;
                this.rejectedShow = false
                this.getList();
            }
        },
        // 获取列表数据
        async getList() {
            let params = {
                page: 1,
                pageSize: 10,
            };
            if (this.tabName === '待确认') {
                params.confirm_status = 0;
            } else if(this.tabName === '已确认') {
                params.confirm_status = 1;
            } else if (this.tabName === '已驳回') {
                params.confirm_status = 2;
            }
            let res = await this.get('/api/agent/reward/receive/confirmedList',params);
            if (res.code === 0) {
                let list = [];
                res.data.list.forEach(e => {
                    list.push({...e,checked: false});
                });
                this.list = [...this.list,...list];
                this.total = res.data.total;
                this.allFlagShow();
            }
        },
        // 勾选平级奖
        changeChecked(row,index) {
            this.list[index].checked = !row.checked;
            if (this.list[index].checked) {
                this.amount_total += this.list[index].amount
            } else {
                this.amount_total -= this.list[index].amount
            }
            this.allFlagShow();
        },
        // 判断当前是否全选
        allFlagShow() {
            let show = false
            this.list.forEach(item => {
                if (item.checked === false) {
                    show = true;
                    return;
                }
            })
            this.allFlag = show ? 0 : 1;
        },
        // 全选功能
        checkAll() {
            //全选功能
            this.allFlag = !this.allFlag;
            if (this.allFlag) {
                let num = 0;
                this.list.forEach(item => {
                    item.checked = true;
                    num += item.amount
                })
                this.amount_total = num
            } else {
                this.list.forEach(item => { item.checked = false;})
                this.amount_total = 0
            }
        },

        // 获取客服二维码
        async getContact() {
            let res = await this.post('/api/agent/reward/receive/contact',{});
            if (res.code === 0) {
                this.image_url = res.data.qr_code;
                this.phone = res.data.phone
            }
        },
        // 切换tabs
        tabsTap(item) {
            this.tabName = item.name;
            this.page = 1;
            this.total = 0;
            this.amount_total = 0;
            this.list = [];
            this.getList();
        }
    },
}
</script>
<style lang="scss" scoped>
.tabs-box {
    width: 750rpx;
    height: 78rpx;
    background: #ffffff;
}
.main-box {
    width: 702rpx;
    height: 132rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-left: 24rpx;
    box-sizing: border-box;
}
.button-box {
    width: 750rpx;
    height: 98rpx;
    background: #FFFFFF;
    position: fixed;
    bottom: 0rpx;
}
.kefu-box {
    width: 96rpx;
    height: 96rpx;
    background: #FFFFFF;
    box-shadow: 0rpx 2rpx 8rpx 1rpx rgba(145,145,145,0.16);
    opacity: 0.98;
    border-radius: 50%;
    position: fixed;
    right: 30rpx;
    bottom: 240rpx;
}
.popup-box {
    width: 500rpx;
    height: 480rpx;
}
.kefu {
    font-weight: 500;
    font-size: 28rpx;
    color: #202020;
}
.icon-box {
    position: fixed;
    top: 1100rpx;
    left: 350rpx
}
.grey {
    font-weight: 400;
    font-size: 26rpx;
    color: #919191;
}
.black {
    font-weight: 500;
    font-size: 26rpx;
    color: #202020
}
.red {
    font-weight: 500;
    font-size: 28rpx;
    color: #F15353;
}
.flc {
    flex-direction: column;
}
.fs40 {
    font-size: 40rpx;
}
.fjsa {
    justify-content: space-around;
}
::v-deep .u-button--info {
    width: 180rpx;
    height: 72rpx;
    color: #f14e4e;
    border-color: #f14e4e;
}
</style>