<!-- 物流信息 -->
<template>
  <view>
    <view class="my_wrapper d-f">
      <view class="img">
        <image mode="widthFix" :src="image_url"></image>
      </view>
      <view class="my-value d-f-c">
        <text class="status">物流状态</text>
        <text>承运公司：{{ express_com }}</text>
        <text>运单编号：{{ express_code }}</text>
      </view>
    </view>

    <view v-if="expressList !== null">
      <timeline>
        <block v-for="(item, index) in expressList" :key="index">
          <timelineItem :leftTime="item.time">
            <view class="tripItem">
              <view class="tips">{{ item.status }}</view>
            </view>
          </timelineItem>
        </block>
      </timeline>
    </view>
    <view v-else>
      <u-empty
        mode="data"
        marginTop="100rpx"
        textSize="28rpx"
        iconSize="150"
      ></u-empty>
    </view>
  </view>
</template>

<script>
import timeline from '../components/chenbin-timeline/timeLine.vue'
import timelineItem from '../components/chenbin-timeline/timelineItem.vue'
export default {
  components: {
    timeline,
    timelineItem,
  },
  data() {
    return {
      id: 0,
      expressApi: '',
      expressList: [],
      express_com: '',
      express_code: '',
      image_url: '',
      source: '',
      is_agent: false
    }
  },
  onLoad(options) {
    if ((options.id ?? '') !== '') {
      this.id = options.id
    }
    if ((options.code ?? '') !== '') {
      this.express_code = options.code
    }
    if ((options.company_name ?? '') !== '') {
      this.express_com = options.company_name
    }
    if ((options.source ?? '') !== '') {
      this.source = options.source
    }
    this.image_url = options.image_url ? options.image_url : ''
    this.getIsAgent()
  },
  onShow() {
    this.expressData()
  },
  methods: {
    //  是否是代理商
    async getIsAgent() {
      const res = await this.post('/api/agent/check')
      if (res.code === 0) {
        this.is_agent = res.data.result
      }
    },
    expressData() {
      if (this.id) {
        this.expressApi = `/api/order/express?id=${this.id}`
      }else {
        this.expressApi = `/api/shipping/info?code=${this.express_code}&company_code=${this.express_com}`
      }
      if (this.source) {
        this.expressApi = '/api/smallShop/smallShopOrder/getExpressInfo'
        const params = {
          id: Number(this.id),
        }
        this.postExpress(params)
      } else {
        this.getExpress()
      }
    },
    getExpress() {
      this.get(this.expressApi, {}, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            if (this.id) {
              this.expressList = data.reshipping[0]?.data.List
              this.express_com = data.reshipping[0]?.express_com
              this.express_code = data.reshipping[0]?.express_code
            } else {
              this.expressList = data.reshipping?.data?.List
              // this.express_com = data.reshipping.express_com;
              // this.express_code = data.reshipping.express_code;
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    postExpress(params) {
      this.post(this.expressApi, params, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            if (this.id) {
              this.expressList = data.reshipping[0]?.data.List
              this.express_com = data.reshipping[0]?.express_com
              this.express_code = data.reshipping[0]?.express_code
            } else {
              this.expressList = data.reshipping?.data?.List
              // this.express_com = data.reshipping.express_com;
              // this.express_code = data.reshipping.express_code;
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
  },
}
</script>
<style scoped>
::v-deep .timelineItem .timeItem .leftTime {
  white-space: initial;
}
</style>
<style lang="scss" scoped>
.my_wrapper {
  padding: 20rpx;
  background-color: #fff;
  .img {
    flex: 1;
    image {
      width: 100%;
      height: 146rpx;
    }
  }
  .my-value {
    flex: 3;
    color: #8c8c8c;
    margin-left: 20rpx;
    font-size: 24rpx;
    .status {
      color: #000;
      font-size: 28rpx;
    }
  }
}
.tripItem {
  // height:140rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  margin-bottom: 30rpx;
  .title {
    font-size: 28rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
  }
  .tips {
    font-size: 22rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(153, 153, 153, 1);
    margin-top: 20rpx;
  }
}
</style>
