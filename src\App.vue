<script>
import Vue from 'vue'
export default {
  data() {
    return {
      logoSrc: '',
      // httpUrl:'',
    }
  },
  onLaunch: function (option) {
    const that = this
    const oMeta = document.createElement('meta')
    oMeta.name = 'referrer'
    oMeta.content = 'no-referrer'
    oMeta.id = 'meta_referrer'
    document.getElementsByTagName('head')[0].appendChild(oMeta)
    // this.httpUrl =  document.location.protocol + "//" + window.location.host
    uni.getSystemInfo({
      success: function (e) {
        Vue.prototype.statusBar = e.statusBarHeight
        // #ifndef MP
        if (e.platform == 'android') {
          Vue.prototype.customBar = e.statusBarHeight + 50
        } else {
          Vue.prototype.customBar = e.statusBarHeight + 45
        }
        // #endif
        // #ifdef H5
        Vue.prototype.customBar = e.statusBarHeight + 45
        console.log(Vue.prototype.customBar)
        // #endif

        // #ifdef MP-WEIXIN
        const custom = wx.getMenuButtonBoundingClientRect()
        Vue.prototype.customBar = custom.bottom + custom.top - e.statusBarHeight
        // #endif

        // #ifdef MP-ALIPAY
        Vue.prototype.customBar = e.statusBarHeight + e.titleBarHeight
        // #endif
      },
    })
    // let website = 'https://dev6.yunzmall.com/';
    // #ifdef H5
    if (window.innerWidth > 768) {
      // window.location.hostname
      const oldUrl = window.location.href // 完整域名
      const pathname = window.location.pathname
      const indexHref = oldUrl.lastIndexOf(pathname)
      const extension = oldUrl.substring(indexHref + 1, oldUrl.length)
      const name = oldUrl.substring(0, indexHref)
      let url = ''
      const index = oldUrl.indexOf('?')
      const result = oldUrl.substr(index + 1, oldUrl.length).replace('&', ',')
      let arr = []
      arr = result.split(',')
      if (
        oldUrl.includes(
          'packageA/commodity/commodity_details/commodity_details',
        )
      ) {
        // 商品详情页
        url = name + '/goodsDetail?goods_id=' + arr[0].match(/=(\S*)/)[1]
      }
      if (oldUrl.includes('pages/classify/classify')) {
        // 分类
        url = name + '/goodsList'
      }
      if (
        oldUrl.includes('packageA/search/searchResult/searchResult') &&
        arr[0].search('collection_id') != -1
      ) {
        // 专辑
        url = name + '/albumGoods?album_key=' + arr[0].match(/=(\S*)/)[1]
      }
      if (oldUrl.includes('packageB/notice/noticeDetails')) {
        // 文章
        url = name + '/about?id=' + arr[0].match(/=(\S*)/)[1]
      }
      if (
        oldUrl.includes('packageA/search/searchResult/searchResult') &&
        arr[0].search('value') != -1
      ) {
        // 商品搜索列表
        url = name + '/searchList?keyword=' + arr[0].match(/=(\S*)/)[1]
      }
      if (oldUrl.includes('packageD/shopList/shopList')) {
        // 店铺列表
        url = name + '/shopList'
      }
      if (url == '') {
        url = name
      }
      window.location.href = url
      // window.location.href =
      // console.log(window.location.hostname);
    } else {
      console.log('小屏幕')
      const _url = window.location.href.indexOf('?menu#') // 判断当前链接是否已包含?menu#
      if (
        window.location.href.indexOf('/#/') > -1 &&
        window.location.href.indexOf('?menu#') < 0
      ) {
        console.log('修改#为?menu#')
        // 如果链接上没有?menu#，修改替换当前链接#为?menu#，防止支付失败
        // 例如：https://dev6.yunzmall.com/h5/#/pages/login/login  =》 https://dev6.yunzmall.com/h5/?menu#/pages/login/login
        const pageUrl = window.location.href.replace('#', '?menu#')
        window.location.href = pageUrl
      }
    }
    // #endif
  },
  // watch:{
  // 	"$route.fullPath"(newValue,oldValue){ //监听路由改变给iframe传值
  // 		window.parent.postMessage(newValue,this.httpUrl)
  // 	}
  // },
  // 不要开启app.vue的onShow,不然pc的宽屏功能会获取不到值
  onShow(e) {
    // #ifdef MP-WEIXIN
    if (e.query.scene) {
      let url = decodeURIComponent(e.query.scene)
      let str1 = url.split('&')
      
      let urlList = {}
      str1.forEach(element => {
         let e = element.split("=")
         urlList[e[0]] = e[1]
      });
      if (urlList.invite_code) {
		  	uni.setStorageSync('invite_code', urlList.invite_code)
		  }
    }
    if (e.query.invite_code) {
			uni.setStorageSync('invite_code', e.query.invite_code)
		}
    // #endif
  },
  methods: {
    framework() {
      this.get('/api/home/<USER>', {}, true)
        .then(res => {
          console.log(res)
          if (res.code === 0) {
            console.log(res.data)
            const data = res.data
            console.log(data.header)
            this.logoSrc = data.header?.logo_src
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
  },
  onHide: function () {
    console.log('App Hide')
  },
}
</script>

<style lang="scss">
page {
  font-size: 28rpx;
}

// #ifdef H5
body::-webkit-scrollbar,
html::-webkit-scrollbar {
  display: none;
}

// #endif

// 移除uview-ui全量样式引入，改为按需引入
// @import 'uview-ui/index.scss';
/*每个页面公共css */
@import './mycss/public.css';
// 优化图标字体引入 - 只引入必要的图标样式
// 将大型图标字体文件改为按需引入，减少包体积
// @import './style/css/iconfont.css'; // 74.6KB - 暂时注释，改为按需引入
@import './style/css/mixin.scss';

// 根据实际使用情况按需引入以下图标样式
// @import './style/css/iconfontAgent.css';
// @import './style/css/localLife.css';
// @import './style/css/marketingTool.css';
// @import './style/css/iconfontManage.css';
// @import './style/css/iconfontSetting.css';
// @import './style/css/iconfontCurriculum.css';
// @import './style/css/iconfontTwo.css';
// @import './style/css/iconfontSmallShop.css';
// @import './style/css/iconfontSmallshopFront.css';
// @import './style/css/iconfontMember.css';
// @import './style/css/iconfontAgency.css';
// @import './style/css/iconfontPoint.css';

</style>
