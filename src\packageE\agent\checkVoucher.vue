<template>
    <view>
        <view class="main-box mt_20">
            <view class="black fwb">订单</view>
            <view class="f fac fjsb" v-for="item in order_details" :key="item.order_sn">
                <view class="black mt_30">{{ item.order_sn }}</view>
                <view class="yuan">¥{{ toYuan(item.amount) }}</view>
            </view>
            <view class="f fac fjsb mt_30">
                <view class="black">总计：</view>
                <view class="yuan">¥{{ toYuan(amount_total) }}</view>
            </view>
        </view>
        <view class="main-box mt_20">
            <view class="black fwb mb_30">打款凭证</view>
            <view class="f fac fw">
                <u--image
                    v-for='item in payment_vouchers'
                    :key='item.src'
                    class="mb_24 mr_15"
                    :src="item.src"
                    radius="10rpx"
                    width="202rpx"
                    height="202rpx"
                ></u--image>
            </view>
        </view>
        <view class="main-box mt_20" v-if="type == 2">
            <view class="black fwb mb_30">驳回理由</view>
            <view class="f fac fw">
            </view>
        </view>
        <view class="button-box f fac fjsb" v-if="type == 0">
            <view class="f fac ml_30">
                <view>
                    <text class="red">￥{{ toYuan(amount_total) }}</text>
                </view>
            </view>
            <view class="f fac mr_25">
                <u-button class="mr_15" shape="circle" text="驳回" @click="rejectedShow = true"></u-button>
                <u-button style="width: 180rpx;" shape="circle" color="#F14E4E" text="确认收款" @click="isShow = true"></u-button>
            </view>
         </view>
         <u-modal :show="rejectedShow" title="驳回理由" width="500rpx" @confirm="rejectedFun" @cancel="rejectedShow = false;value1 = ''" confirmColor="#F14E4E" confirmText="驳回" :showCancelButton="true">
            <u--textarea height="187rpx" width="452rpx" v-model="value1" placeholder="请输入" ></u--textarea>
        </u-modal>
        <!-- 确认收款提示 -->
        <u-modal :show="isShow" showCancelButton @cancel='isShow = false' @confirm='confirm'>
            <p class="fs_30">是否确认收款 ！</p>
        </u-modal>
    </view>
</template>
<script>
export default {
    name: 'checkVoucher',
    data() {
        return {
            rejectedShow: false,
            value1: '', // 驳回理由
            id: null,
            type: 0,

            reject_reason: '', // 驳回理由
            payment_vouchers: [], // 打款凭证
            order_details: [], // 订单信息
            amount_total: 0, // 总计

            isShow: false, // 确认收款弹窗
        }
    },
    onLoad(options) {
        this.id = options.id;
        this.type = options.type;
        this.getVoucher();
    },
    methods: {
        async getVoucher() {
            let res = await this.get('/api/agent/reward/receive/voucher',{id: this.id});
            if (res.code === 0) {
                this.amount_total = res.data.amount_total;
                this.order_details = res.data.order_details;
                this.payment_vouchers = res.data.payment_vouchers;
                this.reject_reason = res.data.reject_reason;
            }
        },
        // 驳回
        async rejectedFun() {
            let params = { ids: [ parseInt(this.id) ], reject_reason: this.value1 }
            let res = await this.post('/api/agent/reward/receive/reject',params);
            if (res.code === 0) {
                this.showText(res.msg);
                this.rejectedShow = false;
                // 刷新页面
                this.getVoucher();

            }
        },
        // 确认收款
        async confirm() {
            let params = { ids: [ parseInt(this.id) ] }
            let res = await this.post('/api/agent/reward/receive/confirm',params);
            if (res.code === 0) {
                this.showText(res.msg);
                this.isShow = false;
                // 刷新页面
                this.getVoucher();
            }
        }

    }
}
</script>
<style lang="scss" scoped>
.main-box {
    width: 702rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-left: 24rpx;
    padding-left: 24rpx;
    box-sizing: border-box;
    padding-right: 24rpx;
    padding-bottom: 30rpx;
    padding-top: 24rpx;
}
.yuan {
    font-weight: 500;
    font-size: 26rpx;
    color: #F14E4E;
}
.black {
    font-weight: 400;
    font-size: 26rpx;
    color: #202020;
}
.red {
    font-weight: 500;
    font-size: 28rpx;
    color: #F15353;
}
.fwb {
    font-weight: bold;
    font-size: 28rpx;
}
.button-box {
    width: 750rpx;
    height: 98rpx;
    background: #FFFFFF;
    position: fixed;
    bottom: 0rpx;
}
.fw {
    flex-wrap: wrap;
}
::v-deep .u-button--info {
    width: 180rpx;
    height: 72rpx;
    color: #f14e4e;
    border-color: #f14e4e;
}
</style>