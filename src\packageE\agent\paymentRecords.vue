<template>
    <view>
        <view class="search-box">
            <u-search
                height="64rpx"
                placeholder="请输入会员/手机号"
                clearabled
                :showAction="false"
                searchIconSize="40"
                v-model="receive_kwd"
                @change="search"
            ></u-search>
        </view>
        <view class="tabs-box">
            <u-tabs 
                :list="tabList"
                :scrollable="false"
                lineWidth="30"
                lineHeight="5"
                lineColor="#F15353"
                :activeStyle="{
                    color: '#F15353',
                    fontWeight: 'bold',
                }"
                :inactiveStyle="{
                    color: '#606266',
                }"
                itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
                @click="tabsTap"
            >
            </u-tabs>
        </view>
        <template v-for="item in list">
            <view class="main-box mt_20 pl_25 pr_25" :key="item.id" @click="navTo('/packageE/agent/payVoucher?id=' + item.id)">
                <view class="f fac fjsb">
                    <view>
                        <view style="width: 610rpx;" class="f fac fjsb pt_30">
                            <view class="black f fac">
                                <view class="f-wb">[{{ item.receive_user.id }}] {{ item.receive_user.nickname }}</view>
                            </view>
                            <view class="grey f fac">
                                <view v-if="tabName === '待确认记录'">待确认</view>
                                <view v-if="tabName === '已确认记录'">已确认</view>
                                <view v-if="tabName === '已驳回记录'">已驳回</view>
                            </view>
                        </view>
                        <view style="width: 620rpx;" class="f fac fjsb mt_20 grey">
                            <view>{{ formatDateTime(item.created_at) }}</view>
                            <view class="mr_10">￥{{ toYuan(item.pay_total_amount) }}</view>
                        </view>
                    </view>
                    <view><u-icon name="arrow-right" color="#101010" size="20"></u-icon></view>
                </view>
            </view>
        </template>
    </view>
</template>
<script>
export default {
    name:'paymentRecords',
    data() {
        return {
            tabList: [
                {
                    name: '待确认记录',
                }, 
                {
                    name: '已确认记录',
                }, 
                {
                    name: '已驳回记录',
                }
            ],
            tabName: '待确认记录',
            receive_kwd: '',
            list: [],
            page: 1,
            pageSize: 10,
            total: 0,
        }
    },
    onLoad() {
        this.getList();
    },
    // 下拉刷新
    onReachBottom() {
        if (this.list.length < this.total) {
            this.page = this.page + 1;
            this.getList();
        }
    },
    methods: {
        search() {
            this.page = 1;
            this.list = [];
            this.total = 0;
            this.getList();
        },
        // 获取列表
        async getList() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
            };
            // 确认状态:0=待确认,1=已确认,2=已驳回
            if (this.tabName == '待确认记录') {
                params.confirm_status = 0
            } else if (this.tabName == '已确认记录') {
                params.confirm_status = 1
            } else {
                params.confirm_status = 2
            }
            if (this.receive_kwd) {
                params.receive_kwd = this.receive_kwd;
            }
            let res = await this.get('/api/agent/reward/pay/paymentLog',params);
            if (res.code === 0) {
                this.list = [...this.list,...res.data.list];
                this.total = res.data.total;
            }
        },
        // 切换tabs
        tabsTap(item) {
            this.tabName = item.name;
            this.page = 1;
            this.total = 0;
            this.list = [];
            this.getList();
        }
    }
}
</script>
<style lang="scss" scoped>
.search-box {
    width: 750rpx;
    height: 96rpx;
    background: #FFFFFF;
    padding: 16rpx 24rpx;
    box-sizing: border-box;
}
.tabs-box {
    width: 750rpx;
    height: 78rpx;
    background: #ffffff;
}
.main-box {
    width: 702rpx;
    height: 132rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-left: 24rpx;
    box-sizing: border-box;
}
.grey {
    font-weight: 400;
    font-size: 26rpx;
    color: #919191;
}
.black {
    font-weight: 500;
    font-size: 26rpx;
    color: #202020
}
.red {
    font-weight: 500;
    font-size: 28rpx;
    color: #F15353;
}
</style>