<template>
    <view>
         <view class="tabs-box">
            <u-tabs 
                :list="tabList"
                :scrollable="false"
                lineWidth="30"
                lineHeight="5"
                lineColor="#F15353"
                :activeStyle="{
                    color: '#F15353',
                    fontWeight: 'bold',
                }"
                :inactiveStyle="{
                    color: '#606266',
                }"
                itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
                @click="tabsTap"
            >
            </u-tabs>
         </view>
         <template v-for='item in list'>
            <view class="main-box mt_20 pl_25 pr_25" :key="item.id">
               <view class="f fac fjsb pt_30">
                   <view class="black">订单号：{{ item.order_sn }}</view>
                   <view class="red">￥{{ toYuan(item.amount) }}</view>
               </view>
               <view class="f fac fjsb mt_20 grey">
                   <view>{{ formatDateTime(item.created_at) }}</view>
                   <view v-if="item.award_status == 1">已打款</view>
                   <view v-else>未打款</view>
               </view>
            </view>
         </template>

         <view class="kefu-box f fac flc" @click="show = true">
            <view class="iconfont icon-linedesign-20 fs40 mt_5"></view>
            <view>客服</view>
         </view>
         <u-popup :show="show" mode="center" round="24rpx" @close="show = false">
            <view class="popup-box f fac flc fjsa">
                <view class="kefu">联系客服</view>
                <view>
                    <u--image
                        width="240rpx"
                        height="240rpx"
                        :src="image_url"
                    ></u--image>
                </view>
                <view class="black">电话：{{ phone }}</view>
            </view>
            <view class="icon-box" @click="show = false"><u-icon name="close-circle" color="#D6D6DC" size="64"></u-icon></view>
        </u-popup>
    </view>
</template>
<script>
export default {
    name: 'levelAward',
    data() {
        return {
            show:false,
            tabList: [
                {
                    name: '全部',
                }, 
                {
                    name: '未打款',
                }, 
                {
                    name: '已打款'
                }
            ],
            tabName: '全部',
            page: 1,
            pageSize: 10,
            total: 0,
            list: [],
            image_url: '',
            phone: ''
        }
    },
    onLoad() {
        this.getList();
        this.getContact();
    },
    // 下拉刷新
    onReachBottom() {
        if (this.list.length < this.total) {
            this.page = this.page + 1;
            this.getList();
        };
    },
    methods: {
        // 获取数据
        async getList() {
            let params = {
                page: this.page,
                pageSize: this.pageSize
            }
            if (this.tabName === '未打款') {
                params.award_status = 0;
            } else if(this.tabName === '已打款') {
                params.award_status = 1;
            }
            let res = await this.get('/api/agent/reward/receive/list',params);
            if (res.code === 0) {
                this.list = [...this.list,...res.data.list];
                this.total = res.data.total;
            }
        },
        // 获取客服数据
        async getContact() {
            let res = await this.post('/api/agent/reward/receive/contact',{});
            if (res.code === 0) {
                this.image_url = res.data.qr_code;
                this.phone = res.data.phone
            }
        },
        // 切换tabs
        tabsTap(item) {
            this.tabName = item.name;
            this.page = 1;
            this.total = 0;
            this.list = [];
            this.getList();
        }
    }
}
</script>
<style lang="scss" scoped>
.tabs-box {
    width: 750rpx;
    height: 78rpx;
    background: #ffffff;
}
.main-box {
    width: 702rpx;
    height: 132rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-left: 24rpx;
    box-sizing: border-box;
}
.kefu-box {
    width: 96rpx;
    height: 96rpx;
    background: #FFFFFF;
    box-shadow: 0rpx 2rpx 8rpx 1rpx rgba(145,145,145,0.16);
    opacity: 0.98;
    border-radius: 50%;
    position: fixed;
    right: 30rpx;
    bottom: 240rpx;
}
.popup-box {
    width: 500rpx;
    height: 480rpx;
}
.kefu {
    font-weight: 500;
    font-size: 28rpx;
    color: #202020;
}
.icon-box {
    position: fixed;
    top: 1100rpx;
    left: 350rpx
}
.grey {
    font-weight: 400;
    font-size: 26rpx;
    color: #919191;
}
.black {
    font-weight: 500;
    font-size: 26rpx;
    color: #202020
}
.red {
    font-weight: 500;
    font-size: 28rpx;
    color: #F15353;
}
.flc {
    flex-direction: column;
}
.fs40 {
    font-size: 40rpx;
}
.fjsa {
    justify-content: space-around;
}
</style>