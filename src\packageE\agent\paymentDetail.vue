<template>
    <view>
        <view class="main-box mt_20">
            <view class="black fwb mb_30">收款代理商信息</view>
            <view class="f fw">
                <view class="">支付宝收款码</view>
                <u--image   
                    class="mb_24 ml_25"
                    :src="agent_payment_info.alipay_pay_code"
                    radius="10rpx"
                    width="202rpx"
                    height="202rpx"
                ></u--image>
            </view>
            <view class="f fw mt_40">
                <view>银行收款账号</view>
                <view class="ml_25">{{ agent_payment_info.bank_account }}</view>
            </view>
            <view class="f fw mt_40">
                <view class="">微信收款码</view>
                <u--image   
                    class="mb_24 ml_25"
                    :src="agent_payment_info.wechat_pay_code"
                    radius="10rpx"
                    width="202rpx"
                    height="202rpx"
                ></u--image>
            </view>
        </view>
        <view class="main-box mt_20">
            <view class="black fwb">订单</view>
            <view class="f fac fjsb" v-for="item in order_info.order_details" :key="item.order_sn">
                <view class="black mt_30">{{ item.order_sn }}</view>
                <view class="yuan">¥{{ toYuan(item.amount) }}</view>
            </view>
            <view class="f fac fjsb mt_30">
                <view class="black">总计：</view>
                <view class="yuan">¥{{ toYuan(order_info.amount_total) }}</view>
            </view>
        </view>
        <view class="main-box mt_20">
            <view class="black fwb mb_30">打款凭证（{{ this.wechat_code.length }}/5）</view>
            <view class="f fac fw">
                <u-upload
                    :fileList="wechat_code"
                    @afterRead="afterWeChatCode"
                    :previewFullImage="true"
                    @delete="deleteWeChatCode"
                    width="204rpx"
                    height="204rpx"
                    uploadIcon="plus"
                    :maxCount="5"
                ></u-upload>
            </view>
        </view>
        <view style="height: 98rpx;margin-top: 30rpx;"></view>
        <view class="button-box f fac fjsb">
            <u-button class="mr_15" shape="circle" text="取消"></u-button>
            <u-button style="width: 320rpx;" shape="circle" color="#F14E4E" text="确认" @click="remit"></u-button>
        </view>

        <u-modal :show="show" showCancelButton @cancel='show = false' @confirm='confirm'>
            <p class="fs_30">是否确认打款 ！</p>
        </u-modal>
    </view>
</template>
<script>
export default {
    name: 'paymentDetail',
    data() {
        return {
            wechat_code: [], // 打款凭证

            show: false, // 

            ids: [],
            agent_payment_info: [],
            order_info: [],
        }
    },
    onLoad(options) {
        let ids = options.ids.split(',');
        ids.forEach(element => {
            this.ids.push(parseInt(element))
        });
        this.getInfo();
    },
    methods: {
        async getInfo() {
            let params = {
                ids: this.ids
            }
            let res = await this.post('/api/agent/reward/pay/info',params);
            if (res.code === 0) {
                this.agent_payment_info = res.data.agent_payment_info;
                this.order_info = res.data.order_info;
            }
        },
        // 确认按钮
        async remit() {
            this.show = true;
        },
        // 确认 打款
        async confirm() {
            let payment_vouchers = [];
            this.wechat_code.forEach(item => {
                payment_vouchers.push({
                    src: item.url.file.url
                })
            })
            let params = {
                ids: this.ids,
                payment_vouchers
            };
            let res = await this.post('/api/agent/reward/pay/payment',params);
            if (res.code === 0) {
                this.showText(res.msg);
                this.show = false
                uni.navigateBack()
            }
        },
         // 图片上传
        async afterWeChatCode(event) {
            let lists = [].concat(event.file);
            let fileListLen = this.wechat_code.length;
            lists.map((item) => {
				this.wechat_code.push({
					...item,
					status: 'uploading',
					message: '上传中'
				})
			})
			for (let i = 0; i < lists.length; i++) {
                let result = await this.uploadFilePromise(lists[i].url);
                let item = this.wechat_code[fileListLen];
                this.wechat_code.splice(fileListLen, 1, Object.assign(item, {
					status: 'success',
					message: '',
					url: result
				}))
				fileListLen++
            }
        },
		uploadFilePromise(url) { //上传图片
            return new Promise((resolve, reject) => {
                let a = uni.uploadFile({
					url: this.api.host + '/api/common/upload',
					filePath: url,
					name:'file',
					formData: {
						file: url
					},
					success: (res) => {
						console.log(res);
						let json = JSON.parse(res.data)
						resolve(json.data);
					}
				});
            })
        },
        deleteWeChatCode(e) {
            this.wechat_code.splice(e.index, 1);
        },
    }
}
</script>
<style lang="scss" scoped>
.main-box {
    width: 702rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-left: 24rpx;
    padding-left: 24rpx;
    box-sizing: border-box;
    padding-right: 24rpx;
    padding-bottom: 30rpx;
    padding-top: 24rpx;
}
.yuan {
    font-weight: 500;
    font-size: 26rpx;
    color: #F14E4E;
}
.black {
    font-weight: 400;
    font-size: 26rpx;
    color: #202020;
}
.red {
    font-weight: 500;
    font-size: 28rpx;
    color: #F15353;
}
.fwb {
    font-weight: bold;
    font-size: 28rpx;
}
.button-box {
    width: 750rpx;
    height: 110rpx;
    background: #FFFFFF;
    position: fixed;
    bottom: 0rpx;
}
.fw {
    flex-wrap: wrap;
}
::v-deep .u-button--info {
    width: 320rpx;
    height: 80rpx;
    color: #f14e4e;
    border-color: #f14e4e;
}

.fs_30 {
  font-size: 30rpx;
}
</style>