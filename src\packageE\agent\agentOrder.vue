<!-- 我的订单 -->
<template>
  <view class="myOrder">
    <u-sticky bgColor="#fff" offset-top="0">
      <view class="search">
        <u-search
          placeholder="请输入收货人/手机号/订单号"
          :clearabled="true"
          :showAction="false"
          v-model="search"
          height="60rpx"
          searchIconSize="45"
          @change="orderSearch"
        ></u-search>
      </view>
      <view class="order_tab">
        <!--lineWidth="60" -->
        <u-tabs
          :list="listTab"
          lineWidth="60rpx"
          :current="index"
          :lineHeight="listTab.length"
          lineColor="#f56c6c"
          :scrollable="false"
          keyName="title"
          ellipsis="false"
          :activeStyle="{
            color: '#f14e4e',
            fontSize: '30rpx',
          }"
          :inactiveStyle="{
            color: '#0c0d0e',
            fontSize: '30rpx',
          }"
          @click="tabsTap"
          itemStyle="height: 88rpx;text-align:center;background:#fff"
        ></u-tabs>
      </view>
    </u-sticky>
    <view
      class="order_content"
      v-if="
        listTab[index] &&
        listTab[index].goodsList &&
        listTab[index].goodsList.length !== 0
      "
    >
      <block
        v-for="(item, goodsindex) in listTab[index].goodsList"
        :key="goodsindex"
      >
        <view class="order_item mb_20">
          <view class="title d-bf mb_10">
            <view class="title_left d-c">
              <u-checkbox-group v-if="item.status === 0 && tabStatus !== ''">
                <u-checkbox
                  size="30"
                  activeColor="#f14e4e"
                  shape="circle"
                  :name="item.id"
                  :checked="item.checked"
                  @change="firmSelectedAll(item, goodsindex)"
                ></u-checkbox>
              </u-checkbox-group>
              <view style="width: 620rpx" class="f fjsb">
                <text class="font_size12 c-666">订单号：{{ item.order_sn }}</text>
                <text class="red" v-if="item.status === 0">待付款</text>
                <text class="red" v-if="item.status === 1" >待发货</text>
                <text class="red" v-if="item.status === 2" >待收货</text>
                <text class="red" v-if="item.status === 3" >已完成</text>
              </view>
              <view class="take ml_23" v-if="item.shipping_method">
                {{ item.shipping_method }}
              </view>
            </view>
            <view class="title_right">{{ item.status_name }}</view>
          </view>
          <u-line color="#ebebeb"></u-line>
          <!-- <view class="mt_10">{{ item.application_shop.shop_name }}</view> -->
          <!-- 商品信息 -->
          <!-- 电影票 -->
          <!-- <view v-if="isCinema" class="order_detail mt_38">
            <view class="mb_10 d-bf">
              <view class="d-cf">
                <text class="font_size16 f-bold">{{ item.title }}</text>
              </view>
            </view>
            <view class="detail_msg d-f">
              <view class="detail_img mr_20">
                <image :src="item.movie_pic"></image>
              </view>
              <view>
                <view>{{ item.movie_title }} {{ item.quantity }}张</view>
                <view style="color: #76777d" class="mt_40">
                  <view>{{ item.orderDesc.split('\r')[0] }}</view>
                  <view class="mt_10 seat">{{ item.orderDesc.split('\r')[1] }} {{ item.seat }} </view>
                </view>
              </view>
            </view>
            <u-line color="#ebebeb"></u-line>
            <view style="color: #76777d" class="mt_10">
              总价：￥{{ item.total_price || '0.00' }}
            </view>
          </view> -->
          <!-- 其他商品 -->
          <view class="order_detail mt_38">
            <view class="mb_40 d-bf">
              <view class="d-cf">
                <text class="iconfont icon-fontclass-dianpu"></text>
                <text class="font_size16 f-bold">{{ item.title }}</text>
              </view>
              <view class="iconfont icon-member_right"></view>
            </view>
            <block
              v-for="(orderItem, orderIndex) in item.order_items"
              :key="orderIndex"
            >
              <view
                class="detail_msg d-f"
                @click="
                  navTo(
                    `/packageE/agent/orderDetail?order_id=${item.id}`,
                  )
                "
              >
                <view class="detail_img mr_20">
                  <image :src="orderItem.image_url"></image>
                </view>
                <view class="detail_wrap">
                  <text style="color: #333">{{ orderItem.title }}</text>
                  <view class="d-bc mt_18">
                    <view class="c-666 font_size12">
                      规格：{{ orderItem.sku_title }}
                    </view>
                    <view class="font_size14 c-333">x {{ orderItem.qty }}</view>
                  </view>

                  <view class="price d-ef mt_18" v-if="orderItem.price">
                    ￥{{ orderItem.price || '0.00' }}
                  </view>
                </view>
              </view>
              <view class="evaluate d-ef mb-10 mt-10" v-if="item.comment_status !== 1">
                <!-- 								<view class="evaluate-btn color-btn"
									@click="navTo('/packageB/member/orderCommentDetails?productId=' + orderItem.product_id)">
									查看评价</view> -->
                <view
                  class="evaluate-btn"
                  v-if="orderItem.comment_status === 0"
                  @click="evaluateBtn(orderItem)"
                >
                  评价
                </view>
              </view>
            </block>
          </view>
          <view class="detail_info mt_34 c-90 font_size13">
            <view class="mb_15">
              <!-- <p class="mb_5">{{ item.application_shop.shop_name }}</p> -->
              {{ item.shipping_address && item.shipping_address.realname }}
              {{ item.shipping_address && item.shipping_address.mobile }}
            </view>
            <view class="address">
              {{ item.shipping_address && item.shipping_address.province }}
              {{ item.shipping_address && item.shipping_address.city }}
              {{ item.shipping_address && item.shipping_address.county }}
              {{ item.shipping_address && item.shipping_address.town }}
              {{ item.shipping_address && item.shipping_address.detail }}
            </view>
          </view>
          <view class="d-ef mt_16 font_size14">
            <view class="price">
              共{{ item.goods_count }}件商品 实付：
              <text
                style="font-size: 36rpx; font-weight: 700"
                v-if="item.amount"
              >
                ￥{{ item.amount || '0.00' }}
              </text>
            </view>
          </view>
          <view v-if="item.parent_agent_user.id != 0" class="agent-box mt_20">
            来源代理商：[{{ item.parent_agent_user.id }}]{{ item.parent_agent_user.nickname }}
          </view>
          <view v-if="item.parent_agent_user.id == 0" class="agent-box mt_20">
            来源代理商：[平台]
          </view>
          <view class="d-ef fw mt_20">
            <block
              v-for="(itemBtn, indexBtn) in item.operations"
              :key="indexBtn"
            >
              <!-- 售后中状态是前端判断，所以要抽离出来 -->
              <view
                class="cancel_order d-cc mt_20 ml_20"
                v-if="itemBtn.code !== 'bill' || item.order_bill.bill_id > 0"
                @click="orderBtn(itemBtn.code, item.order_items, item.id, item)"
              >
                {{ itemBtn.title }}
              </view>
            </block>
          </view>
        </view>
      </block>
      <view
        v-if="
          isReachBottom &&
          listTab[index] &&
          listTab[index].goodsList &&
          listTab[index].goodsList.length > 0
        "
        class="d-cc fs-1 c-5e5e5e mb-25 mt-25"
      >
        暂无更多~
      </view>
    </view>
    <view class="empty" v-else>
      <u-empty
        mode="order"
        marginTop="100rpx"
        textSize="28rpx"
        iconSize="150"
      ></u-empty>
    </view>
    <view
      class="my_order_pays d-ef"
      v-if="checkedGoods"
      @click="navTo('/packageA/goodsorder/orderpay/orderpay?agent=true')"
    >
      <view class="pay_together d-cc">合并支付</view>
    </view>
    <u-modal
      width="300px"
      :content="hintContent"
      :showCancelButton="true"
      :show="orderShow"
      @cancel="orderCancel('order')"
      @confirm="orderConfirm('order')"
    ></u-modal>
    <u-modal
      width="300px"
      :content="receiveContent"
      :showCancelButton="true"
      :show="receiveShow"
      @cancel="orderCancel('receive')"
      @confirm="orderConfirm('receive')"
    ></u-modal>
    <!-- 支付 -->
  </view>
</template>

<script>
import { DebounceOrder } from '@/utils/debounce.js'
export default {
    name:'agentOrder',
    data() {
        return {
        id: 0,
        search: '',
        orderUrl: '', // 后台api接口
        index: 0,
        orderShow: false,
        receiveShow: false,
        showTap: false,
        hintContent: '是否取消订单',
        receiveContent: '是否确认收货',
        listTab: [], // 导航栏，和订单数据
        orderId: [], // 合并支付ID
        orderListApi: '', // 数据列表请求api
        tabStatus: '', // 订单状态
        // 基本案列数据
        checkedGoods: false,
        isReachBottom: false, // 列表到底
        apiUrl: 'api/order/list?status=0',
        Refresh: 'loadmore',
        total: null, // 总共多少条数据
        formData: {
            pageSize: 10, // 每页10条数据
            page: 1, // 第几页
        },
        statusIndex: null,
        detailImg:
            'https://dev8.yunzmall.com/static/upload/newimage/a76f8983d6913ede16d1ba60abbb38f8.png',
        }
    },
    mounted() {},

    onLoad(options) {   
        this.search = options.id ? options.id : ''
        if ((options.status ?? '') !== '') {
        this.tabStatus = options.status
        const statusIndex = parseInt(this.tabStatus)
        this.statusIndex = statusIndex
        // if(uni.getStorageSync('memberKey') == 'cinema') { 
        //     if(statusIndex === 10) {
        //     this.index = 1
        //     } else {
        //     this.index = statusIndex + 2 // 跳转到订单列表获取对应的索引
        //     }
        // } else {
            this.index = statusIndex + 1 // 跳转到订单列表获取对应的索引
        // }
        } else {
        this.index = 0
        }
    },
    filters: {
        handleprice(value) {
        let result = parseFloat(value)
        if (isNaN(result)) {
            this.toast('传递参数错误，请检查！')
            return false
        }
        result = Math.round(value * 100) / 100
        return result
        },
    },
    onReachBottom() {
        const { page, totalPage } = this.listTab[this.index]
        if (page >= totalPage) {
        console.log('没有更多')
        this.isReachBottom = true
        return
        }
        this.formData.page = page + 1
        this.orderList(true)
    },
    onShow() {
        uni.$on('refreshData', () => {
        // 返回上一页刷新数据
        this.orderList()
        })
        this.orderList()
        // if( uni.getStorageSync('memberKey') === 'cinema' ) {
        // this.isCinema = true
        // } else {
        // this.isCinema = false
        // }
    },
    onUnload() {
        // 移除监听事件
        uni.$off('refreshData')
    },
    onHide() {
        // 返回上一页刷新数据
        this.listTab[this.index].goodsList = []
    },
    onPullDownRefresh() {
        this.formData.page = 1
        // 调用获取数据方法
        this.listTab.map((item, index) => {
        item.goodsList = []
        })
        this.orderList()
        setTimeout(() => {
        // 结束下拉刷新
        uni.stopPullDownRefresh()
        }, 1000)
    },
    methods: {
        orderSearch: DebounceOrder(function (e) {
        if ((this.search ?? '') !== '') {
            this.formData.page = 1
            this.listTab[this.index].goodsList = []
            this.orderList()
        }
        }, 1000),
        tabsTap(item) {
        this.index = item.index
        this.search = ''
        // this.formData.page = 1;
        if (item.url.indexOf('?') != -1) {
            // 截取链接上的状态码，可以前端自定义
            const str = item.url.substr(0)
            const strs = str.split('=')
            this.tabStatus = strs[1]
        } else {
            this.tabStatus = ''
        }

        if (item.goodsList.length === 0) {
            // 数据为空的数据再加载
            this.formData.page = 1
            this.orderList()
        } else {
            if (this.formData.page > 1) {
            this.formData.page = 1
            this.orderList()
            }
        }
    },
    firmSelectedAll(item, index) {
      // 选择订单去支付
      const orderId = []
      const offCarArr = []
      const goodsLists = this.listTab[this.index].goodsList // 当前列表所有数据
      const goodsListItem = this.listTab[this.index].goodsList[index] // 获取当前列表的item数据
      goodsListItem.checked = !item.checked
      for (const i in goodsLists) {
        if (goodsLists[i].checked) {
          this.checkedGoods = true // 如果有选择就显示合并支付
          orderId.push(goodsLists[i].id)
        }
      }
      const orderIds = [...new Set(orderId)] // 去重
      goodsLists.forEach(item =>
        item.checked == true ? offCarArr.push(item) : '',
      )
      const allChecks = offCarArr.every(item => item.checked == false)
      if (allChecks === true) {
        this.checkedGoods = false
      }
      if (orderId.length > 0) {
        uni.setStorageSync('orderIds', orderIds)
      }
    },
    orderCancel(type) {
      if (type === 'order') {
        this.orderShow = false
      } else {
        this.receiveShow = false
      }
    },
    orderConfirm(type) {
      // 确定收货和取消订单
      if (type === 'receive') {
        this.orderUrl = `/api/agent/purchaseOrder/receive`
      } else {
        this.orderUrl = `/api/agent/purchaseOrder/cancel`
      }
      this.post(this.orderUrl, {order_id:this.id}, true)
        .then(res => {
          if (res.code === 0) {
            const data = res.data
            this.orderShow = false
            this.receiveShow = false
            setTimeout(() => {
              this.toast(res.msg)
            }, 1000)

            this.formData.page = 1
            this.listTab[this.index].goodsList = []
            this.orderList()
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          // console.log(Error);
        })
    },
    orderBtn(code, order_items, id, item) {
      // 订单的按钮事件
      this.id = id // 订单ID
      if (code === 'close') {
        // 关闭订单
        this.orderShow = true
      } else if (code === 'pay' && this.id) {
        // 跳转到支付
        const orderId = []
        orderId.push(this.id)
        uni.setStorageSync('orderIds', orderId)
        uni.redirectTo({
          url: '/packageA/goodsorder/orderpay/orderpay?agent=true',
        })
      } else if (code === 'refund') {
        // 退款退货
        if (order_items.length > 1) {
          uni.navigateTo({
            url: `/packageB/member/evaluateList?refundId=${this.id}`,
          })
        } else {
          if (order_items[0].can_refund) {
            // 可以退款（1是0否）
            if (order_items[0].refund_status === 2) {
              uni.navigateTo({
                url: `/packageB/member/aftersales?afterSaleId=${order_items[0].id}&order=order`,
              })
            } else {
              uni.navigateTo({
                url: `/packageB/member/refund?id=${order_items[0].id}`,
              })
            }
          } else {
            this.toast('订单不支持退款')
          }
        }
      } else if (code === 'express_info') {
        // 物流信息
        const image_url = order_items.length ? order_items[0].image_url : ''
        uni.navigateTo({
          url:
            '/packageB/member/logistics?id=' +
            this.id +
            '&image_url=' +
            image_url,
        })
      } else if (code === 'receive') {
        // 确认收货
        this.receiveShow = true
      } else if (code === 'bill') {
        // 物流信息
        uni.navigateTo({
          url: `/packageC/invoiceDetails/invoiceDetails?id=${item.order_bill.bill_id}&type=order`,
        })
      } else {
        // console.log('111111');
      }
    },
    async orderList(more) {
      // if(uni.getStorageSync('memberKey') == 'fulu') {
      //   if (this.tabStatus == '') {
      //     this.orderListApi = `/api/fuluSupply/api/order/list?page=${this.formData.page}&pageSize=${this.formData.pageSize}&order_sn=${this.search}&start_at=&end_at=`
      //   } else {
      //     this.orderListApi = `/api/fuluSupply/api/order/list?page=${this.formData.page}&pageSize=${this.formData.pageSize}&order_sn=${this.search}&start_at=&end_at=&status=${this.tabStatus}`
      //   }
      // }
      // if(uni.getStorageSync('memberKey') == 'cake') {
      //   if (this.tabStatus == '') {
      //     this.orderListApi = `/api/cake/getLocalOrderList?page=${this.formData.page}&pageSize=${this.formData.pageSize}&order_sn=${this.search}&start_at=&end_at=`
      //   } else {
      //     this.orderListApi = `/api/cake/getLocalOrderList?page=${this.formData.page}&pageSize=${this.formData.pageSize}&order_sn=${this.search}&start_at=&end_at=&status=${this.tabStatus}`
      //   }
      // }
      // if(uni.getStorageSync('memberKey') == 'cinema') {
      //   if (this.tabStatus == '') {
      //     this.orderListApi = `/api/cinemaTicket/orderList`
      //   } else {
      //     this.orderListApi = `/api/cinemaTicket/orderList`
      //   }
      // }
      // if(uni.getStorageSync('memberKey') == 'order') {
      //   // 订单列表
      //   if (this.tabStatus == '') {
          // this.orderListApi = `/api/order/list?page=${this.formData.page}&pageSize=${this.formData.pageSize}&order_sn=${this.search}&start_at=&end_at=`
      //   } else {
      //   }
      // }
      // let res = ''
      // if (uni.getStorageSync('memberKey') == 'cinema') {
      //   if (this.tabStatus == '') {
      //     res = await this.post(this.orderListApi, { page: this.formData.page, pageSize: this.formData.pageSize, order_sn: this.search, start_at: '', end_at: '' }, true)
      //   } else {
      //     res = await this.post(this.orderListApi, { page: this.formData.page, pageSize: this.formData.pageSize, order_sn: this.search, status: this.tabStatus, start_at: '', end_at: ''  }, true)
      //   }
      // } else {
      const params = {
        page:this.formData.page,
        pageSize: this.formData.pageSize,
        keyword: this.search,
      }
      if (this.tabStatus !== '') {
        params.status = this.tabStatus
      }
      // ?page=${this.formData.page}&pageSize=${this.formData.pageSize}&keyword=${this.search}&status=${this.tabStatus}
      this.orderListApi = `/api/agent/purchaseOrder/list`
      const  res = await this.get(this.orderListApi, params, true)
      // }
        // .then(res => {
          if (res.code === 0) {
            const data = res.data
            // this.listTab = data.tags;
            this.total = data.total
            const newlist = data.list ? data.list : []
            const listTab = data.tags
            if (this.listTab.length === 0) {
              listTab.map((item, index) => {
                // console.log(this.tabStatus);
                listTab[index].goodsList = []
                listTab[index].page = 1
              })
              this.listTab = listTab
            }
            if (more) {
              // 下拉加载下一页
              // console.log(more)
              this.listTab[this.index].page = this.listTab[this.index].page + 1
              this.listTab.forEach((item, index) => {
                // 加载第二页的时候，切换其他页面清空数组
                if (index !== this.index) {
                  item.goodsList = []
                }
              })
            }
            this.listTab.map((item, index) => {
              if (this.index == index) {
                this.listTab[index].goodsList.push(...newlist)
              }
            })
            const totalPage = Number(data.total) / Number(data.pageSize)
            this.listTab[this.index].totalPage = Math.ceil(totalPage)

            for (const i in newlist) {
              this.$set(newlist[i], 'checked', false)
              newlist[i].amount = this.toYuan(newlist[i].amount)
              for (const j in newlist[i].order_items) {
                newlist[i].order_items[j].price = this.toYuan(
                  newlist[i].order_items[j].price,
                )
                newlist[i].order_items[j].amount = this.toYuan(
                  newlist[i].order_items[j].amount,
                )
                for (const k in newlist[i].operations) {
                  // 处理后端返回的按钮,售后状态信息
                  if (
                    newlist[i].order_items.length == 1 &&
                    newlist[i].operations[k].code === 'refund' &&
                    newlist[i].order_items[0].refund_status === 2
                  ) {
                    newlist[i].operations[k].title = '售后中'
                  }
                }
              }
            }
          } else {
            this.toast(res.msg)
          }
        /* })
        .catch(Error => {
          // console.log(Error);
        }) */
    },
    evaluateBtn(item) {
      const details = JSON.stringify(item)
      uni.navigateTo({
        url: `/packageB/member/evaluate?details=${details}`,
      })
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .u-modal__content {
  text-align: center;
}

.myOrder ::v-deep .u-sticky {
  top: 0 !important;
}
</style>
<style lang="scss" scoped>
.search {
  padding: 14rpx 30rpx;
  background-color: #fff;
}

.order_tab {
  background-color: #fff;
}

.order_content {
  margin: 20rpx 30rpx 0rpx 30rpx;
  padding-bottom: 100rpx;

  .order_item {
    width: 100%;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 28rpx 20rpx 28rpx 20rpx;
    box-sizing: border-box;

    .title {
      .title_left {
        .take {
          width: 70rpx;
          height: 34rpx;
          line-height: 34rpx;
          text-align: center;
          background-color: #fcbc07;
          border-radius: 10rpx;
          font-size: 24rpx;
          color: #fff;
        }
      }

      .title_right {
        font-size: 24rpx;
        color: #f14e4e;
      }
    }

    .order_detail {
      .icon-fontclass-dianpu {
        color: #f14e4e;
        font-size: 54rpx;
        margin-right: 13rpx;
      }

      .icon-member_right {
        color: #bdbdbd;
      }

      .detail_msg {
        margin-bottom: 20rpx;

        .detail_wrap {
          width: 70%;
        }

        .detail_img {
          border-radius: 15rpx;
          image {
            width: 140rpx;
            height: 160rpx;
          }
        }
      }
      .seat {
        width: 400rpx; 
        white-space: nowrap; 
        overflow: hidden; 
        text-overflow: ellipsis; 
      }

      .evaluate {
        .evaluate-btn {
          color: #565656;
          border-radius: 30rpx;
          margin-right: 20rpx;
          padding: 8rpx 20rpx;
          border: 2rpx solid #9f9c9c;
        }

        .color-btn {
          color: #f15353;
          border-color: #f15353;
        }
      }
    }

    .cancel_order {
      // width: 150rpx;
      // height: 46rpx;
      padding: 8rpx 20rpx;
      border-radius: 28rpx;
      border: solid 2rpx #b4b4b4;
    }

    .order_pay {
      width: 150rpx;
      height: 46rpx;
      background-color: #f14e4e;
      color: #fff;
      border-radius: 23rpx;
    }
  }
}

.my_order_pays {
  width: 100%;
  height: 100rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;

  .pay_together {
    width: 170rpx;
    height: 74rpx;
    background-color: #f15353;
    border-radius: 36rpx;
    color: #fff;
    margin-right: 30rpx;
  }
}
.agent-box {
    width: 650rpx;
    height: 56rpx;
    line-height: 56rpx;
    background: #F6F6F6;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    padding-left: 17rpx;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    box-sizing: border-box;
}
.red {
  color: #F14E4E;
}
</style>
