<template>
  <view>
    <view class="select-box f fac fjsb">
      <view>
        <uni-data-select
          class="material-select"
          :localdata="selectList"
          :clear="false"
          v-model="selectId"
          @change="changeSelect"
        ></uni-data-select>
      </view>
      <view class="f fac fjsb date-box" @click="openDate">
        <view>{{ dateValue }}</view>
        <view>
          <u-icon name="arrow-down"></u-icon>
        </view>
      </view>
      <view class="mr_10 red" @click="empty">清空</view>
    </view>
    <view class="performance-box">业绩统计总金额：{{ toYuan(amountTotal) }}</view>
    <template v-for="(item,index) in list">
      <view class="main-box mt_20" :key="index">
        <view>
          <view class="f fac fjsb mt_20" @click="iconShowChange(item)">
            <view class>{{ item.group_key | dateFilter }}</view>
            <view class="f fac">
              ￥{{ toYuan(item.total_kpi_amount) }}
              <u-icon
                v-if="!iconShow"
                class="ml_15"
                name="arrow-down"
                color="#AAAAB3"
                size="26"
              ></u-icon>
              <u-icon
                v-if="iconShow"
                class="ml_15"
                name="arrow-up"
                color="#AAAAB3"
                size="26"
              ></u-icon>
            </view>
          </view>
          <view v-if="iconShow" class="collapse-item">
            <view v-for="items in groupDetail" :key="items.id">
              <view class="f fac fjsb mt_20">
                <view class="black">{{ items.order_sn }}</view>
                <view class="black">￥{{ toYuan(items.kpi_amount) }}</view>
              </view>
              <view class="f fac fjsb mb_20">
                <view class="grey">
                  时间：{{ formatDateTime(items.created_at) }}
                </view>
                <!-- <view class="grey">已结算</view> -->
              </view>
              <u-line></u-line>
            </view>
          </view>
          <u-line v-if="!iconShow"></u-line>
        </view>
      </view>
    </template>
    <view class="mb_100"></view>
    <!-- 时间 -->
    <u-datetime-picker
      ref="datetimePicker"
      confirmColor="#f15353"
      :show="dateShow"
      v-model="date"
      :title="title"
      :mode="mode"
      itemHeight="100"
      @cancel="dateCancel"
      @confirm="confirmDate"
      @change="changeDate"
    ></u-datetime-picker>
  </view>
</template>
<script>
export default {
  name: 'performanceStats',
  data() {
    return {
      iconShow: false,
      // 选择框数据
      selectList: [
        { text: '年', value: 0 },
        { text: '季度', value: 1 },
        { text: '月', value: 2 },
        { text: '周', value: 3 },
        { text: '日', value: 4 },
      ],
      selectId: 0,
      weeks: [], // 周数据

      dateShow: false,
      title: '请选择年', // 时间弹窗名称
      mode: 'year',
      date: Number(new Date()),

      page: 1,
      pageSize: 10,
      total: 0,
      list: [],

      dateValue: '请确认时间',
      start_at: '',
      end_at: '',
      group_by: '', // 按年，季度，月，周，日分组
      groupDetail: [], // 统计详情数据
      detailPage: 1,
      detailPageSize: 10,
      detailTotal: 0,
      amountTotal: 0, // 业绩统计总金额
    }
  },
  filters: {
    // 时间显示 
    dateFilter(value) {
      let str = value.split('-');
      if (str.length <= 1) {
        return value
      }
      let str1 = []
      let title = '';
      var units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿']
      var digits = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      switch (str[1][0] ) {
        case 'W': // 周
          str1 = value.split('W')
          var result = String(parseInt(str1[1])).replace(/./g, function (digit, index, array) {
            return digits[Number(digit)] + units[array.length - index - 1]
          })
          title =  str[0] + '年 第'  + result + '周'
          break;
        case 'Q': // 季
          str1 = value.split('Q')
          var result = String(parseInt(str1[1])).replace(/./g, function (digit, index, array) {
            return digits[Number(digit)] + units[array.length - index - 1]
          })
          title =  str[0] + '年 第' + result + '季度'
          break;
        default:
          title = value
          break;
      }
      return title      
    }
  },
  onLoad(options) {
    this.getList()
    let year = new Date().getFullYear()
    this.getWeeksInYear(year)
  },
  onReady() {
    // 微信小程序需要用此写法
    this.$refs.datetimePicker.setFormatter(this.formatter)
    // 微信小程序过滤用这种方法 过滤器
    this.$refs.datetimePicker.setFilterDate(this.filterDate)
  },
  async onReachBottom() {
    if (this.list.length < this.total) {
      this.page = this.page + 1
      const params = {
        group_by: this.group_by,
        page: this.page,
        pageSize: this.pageSize,
      }
      if (this.start_at !== '') {
        params.start_at = this.start_at
      }
      if (this.end_at !== '') {
        params.end_at = this.end_at
      }
      const res = await this.get('/api/agent/stats/list', params)
      if (res.code === 0) {
        this.list = this.list.concat(res.data.list)
      }
    }
  },
  methods: {
    // 获取列表
    async getList() {
      let params = {
        group_by: this.group_by,
        page: this.page,
        pageSize: this.pageSize,
      }
      if (this.start_at !== '') {
        params.start_at = this.start_at
      }
      if (this.end_at !== '') {
        params.end_at = this.end_at
      }
      let res = await this.get('/api/agent/stats/list', params)
      if (res.code === 0) {
        this.list = res.data.list
        this.total = res.data.total
        this.amountTotal = res.data.amount_total
      }
    },
    // 清空
    empty() {
      this.changeSelect(0)
      this.group_by = ''
      this.iconShow = false
      this.getList()
    },
    // 展开
    async iconShowChange(item) {
      console.log('asdasda', item);
      
      if (!this.group_by) {
        this.toast('请选择时间')
        return
      }
      this.iconShow = !this.iconShow
      const params = {
        group_key: item.group_key,
        group_by: this.group_by,
        page: this.page,
        pageSize: this.pageSize,
      }
      if (this.start_at !== '') {
        params.start_at = this.start_at
      }
      if (this.end_at !== '') {
        params.end_at = this.end_at
      }
      const res = await this.get('/api/agent/stats/groupDetail', params)
      if (res.code === 0) {
        this.groupDetail = res.data.list
        this.detailTotal = res.data.total
      }
    },
    // 切换时间
    changeSelect(e) {
      this.selectId = e
      this.date = Number(new Date())
      this.dateValue = '请确认时间'
      this.start_at = ''
      this.end_at = ''
      switch (this.selectId) {
        case 0:
          this.title = '请选择年'
          this.mode = 'year'
          break
        case 1:
          this.title = '选择季度'
          this.mode = 'year-month'
          break
        case 2:
          this.title = '选择月'
          this.mode = 'year-month'
          break
        case 3:
          this.title = '选择周'
          this.mode = 'week'
          break
        case 4:
          this.title = '选择日'
          this.mode = 'date'
          break
        default:
          break
      }
    },
    // 过滤器
    filterDate(e, s) {
      let val = []
      switch (this.selectId) {
        case 1: // 季度
          if (e === 'year') {
            val = s
          } else if (e === 'month') {
            val = s.filter(item => 1 === parseInt(item) % 3)
          }
          break
        default:
          val = s
          break
      }
      return val
    },
    // 时间文本
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        if (this.selectId === 1) {
          // 季度
          if (value === '01') {
            return `第一季度`
          }
          if (value === '04') {
            return `第二季度`
          }
          if (value === '07') {
            return `第三季度`
          }
          if (value === '10') {
            return `第四季度`
          }
        }

        return `${value}月`
      }
      if (type === 'day') {
        return `${value}日`
      }
      if (type === 'week') {
        let val = this.numberToChinese(parseInt(value))
        return `第${val}周 (${this.weeks[parseInt(value) - 1].startDate}-${
          this.weeks[parseInt(value) - 1].endDate
        })`
      }
      return value
    },
    // 切换时间除法
    changeDate(type) {
      // 重新计算周多时间
      if (this.selectId == 3) {
        let date = new Date(type.value)
        let y = date.getFullYear()
        this.getWeeksInYear(y)
      }
    },
    openDate() {
      this.dateShow = true
    },
    dateCancel() {
      this.dateShow = false
    },
    // 确认
    async confirmDate(e) {
      this.iconShow = false
      this.page = 1
      let date = new Date(e.value)
      this.dateShow = false
      let y = date.getFullYear()
      let month = String(date.getMonth() + 1);
      let day = String(date.getDate())
      switch (this.selectId) {
        case 0: // 年
          this.group_by = 'year'
          this.dateValue = y + '年'
          this.start_at = y + '-01-01 00:00:00'
          this.end_at = y + '-12-31 23:59:59'
          break
        case 1: // 季
          this.group_by = 'quarter'
          if (month >= 1 && month < 4) {
            this.dateValue = y + '年 第一季度'
            this.start_at = y + '-01-01 00:00:00'
            this.end_at = y + '-03-31 23:59:59'
          } else if (month >= 4 && month < 7) {
            this.dateValue = y + '年 第二季度'
            this.start_at = y + '-04-01 00:00:00'
            this.end_at = y + '-06-30 23:59:59'
          } else if (month >= 7 &&  month < 10) {
            this.dateValue = y + '年 第三季度'
            this.start_at = y + '-07-01 00:00:00'
            this.end_at = y + '-10-31 23:59:59'
          } else if (month >= 10 ) {
            this.dateValue = y + '年 第四季度'
            this.start_at = y + '-10-01 00:00:00'
            this.end_at = y + '-12-31 23:59:59'
          }
          break
        case 2: // 月
          this.group_by = 'month'
          this.dateValue = y + '年 ' + month + '月'
          this.start_at = y + '-' + month.padStart(2, '0') + '-' + '01 00:00:00'
          let m = parseInt(month) + 1 + ''
          this.end_at = y + '-' + m.padStart(2, '0') + '-' + '01 00:00:00'
          break
        case 3: // 周
          this.group_by = 'week'
          let week = this.weeks[this.getYearWeek(e.value) - 1]
          this.dateValue =
            y + '年 第' + this.numberToChinese(week.weekNum) + '周'
          let startDate = week.startDate.split('.')
          let endDate = week.endDate.split('.')
          this.start_at =
            y +
            '-' +
            startDate[0].padStart(2, '0') +
            '-' +
            startDate[1].padStart(2, '0') +
            ' 00:00:00'
          this.end_at =
            y +
            '-' +
            endDate[0].padStart(2, '0') +
            '-' +
            endDate[1].padStart(2, '0') +
            ' 23:59:59'
          break
        case 4: // 日
          this.group_by = 'day'
          this.dateValue = y + '年 ' + month + '月 ' + day + '日'
          this.start_at =
            y +
            '-' +
            month.padStart(2, '0') +
            '-' +
            day.padStart(2, '0') +
            ' 00:00:00'
          this.end_at =
            y +
            '-' +
            month.padStart(2, '0') +
            '-' +
            day.padStart(2, '0') +
            ' 23:59:59'
          break
        default:
          break
      }
      console.log(this.start_at, this.end_at)

      await this.getList()
      this.amountTotal = this.list ? this.list[0].total_kpi_amount : 0;
    },
    // 数字改成大些
    numberToChinese(num) {
      var units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿']
      var digits = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九']

      var result = String(num).replace(/./g, function (digit, index, array) {
        return digits[Number(digit)] + units[array.length - index - 1]
      })

      return result
    },
    // 获取每一周的日期数据
    getWeeksInYear(year) {
      this.weeks = []
      let date = new Date(year, 0, 1) // 1月1日
      let dayOfWeek = date.getDay() ? date.getDay() : 7
			if (dayOfWeek !== 7) {
				date.setDate(date.getDate() + (8 - dayOfWeek) - 1); // 增加日期 到 第一个 星期日
			}
      this.weeks = [
        {
          weekNum: 1,
          startDate: '1.1',
          endDate: '1.' + (8 - dayOfWeek),
        },
      ]    
			date.setDate(date.getDate() + 1); // 增加日期直到下一个周一
      let weekNum = 2
      while (date.getFullYear() == year) {
        // 如果今天为星期天，则添加进新的周
        if (date.getDay() === 1) {
          let datetime = new Date(date.getTime())
          let datetime2 = new Date(date.getTime() + 6 * 24 * 60 * 60 * 1000)
          let month = String(datetime.getMonth() + 1)
          let month2 = String(datetime2.getMonth() + 1)
          let day = String(datetime.getDate())
          let day2 = String(datetime2.getDate())
          this.weeks.push({
            weekNum: weekNum,
            startDate: month + '.' + day,
            endDate: month2 + '.' + day2,
          })
          weekNum++
        }
        // 移动到下一天
        date.setDate(date.getDate() + 1)
      }
      this.weeks[this.weeks.length - 1].endDate = '12.31'
    },
    // 计算第几周
    getYearWeek(endDate) {
      endDate = new Date(endDate)
      //本年的第一天
      var beginDate = new Date(endDate.getFullYear(), 0, 1)
      //星期从0-6,0代表星期天，6代表星期六
      var endWeek = endDate.getDay()
      if (endWeek == 0) endWeek = 7
      var beginWeek = beginDate.getDay()
      if (beginWeek == 0) beginWeek = 7
      //计算两个日期的天数差
      var millisDiff = endDate.getTime() - beginDate.getTime()
      var dayDiff = Math.floor(
        (millisDiff + (beginWeek - endWeek) * (24 * 60 * 60 * 1000)) / 86400000,
      )
      return Math.ceil(dayDiff / 7) + 1
    },
  },
}
</script>
<style lang="scss" scoped>
.select-box {
  width: 750rpx;
}
.date-box {
  width: 396rpx;
  height: 64rpx;
  background: #ffffff;
  border-radius: 40rpx;
  padding-left: 33rpx;
  padding-right: 20rpx;
  box-sizing: border-box;
}
.performance-box {
  margin-left: 24rpx;
  font-weight: 400;
  font-size: 26rpx;
  color: #202020;
}
.main-box {
  width: 702rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-left: 24rpx;
  padding-left: 20rpx;
  padding-right: 30rpx;
  padding-top: 36rpx;
  padding-bottom: 60rpx;
  box-sizing: border-box;
  .collapse-item {
    width: 662rpx;
    background: #fafafa;
    border-radius: 20rpx;
    padding: 30rpx 36rpx 31rpx 23rpx;
    box-sizing: border-box;
  }
}
.red {
  font-weight: 400;
  font-size: 26rpx;
  color: #f14e4e;
}
.grey {
  font-weight: normal;
  font-size: 24rpx;
  color: #6e6e79;
}
.black {
  font-weight: 500;
  font-size: 26rpx;
  color: #202020;
}
.material-select ::v-deep .uni-select__input-text {
  width: 160rpx;
}
::v-deep .uni-select {
  padding-left: 10px;
  background-color: #ffffff;
  border-radius: 20px;
  padding-right: 10px;
  height: 64rpx;
}
::v-deep .uni-stat__select {
  padding-right: 0rpx;
}
</style>
