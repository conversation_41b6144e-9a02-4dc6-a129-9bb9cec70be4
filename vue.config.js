// vue.config.js，如没有此文件则手动创建
const path = require('path')

module.exports = {
    transpileDependencies: ['uview-ui'],

    configureWebpack: config => {
        // 针对微信小程序的优化配置
        if (process.env.UNI_PLATFORM === 'mp-weixin') {
            // 优化代码分割
            config.optimization = {
                ...config.optimization,
                splitChunks: {
                    chunks: 'all',
                    cacheGroups: {
                        // 将uview-ui单独打包
                        uview: {
                            name: 'uview-vendor',
                            test: /[\\/]node_modules[\\/]uview-ui[\\/]/,
                            priority: 20,
                            chunks: 'all'
                        },
                        // 将其他第三方库单独打包
                        vendor: {
                            name: 'vendor',
                            test: /[\\/]node_modules[\\/]/,
                            priority: 10,
                            chunks: 'all'
                        },
                        // 公共代码
                        common: {
                            name: 'common',
                            minChunks: 2,
                            priority: 5,
                            chunks: 'all',
                            reuseExistingChunk: true
                        }
                    }
                },
                // 启用tree-shaking
                usedExports: true,
                sideEffects: false
            }

            // 排除不需要的模块和文件
            config.externals = {
                ...config.externals,
                // 在小程序环境中排除一些不需要的模块
                'xgplayer': 'xgplayer',
                'xgplayer-hls': 'xgplayer-hls'
            }

            // 排除PC适配相关文件（小程序不需要）
            config.module.rules.push({
                test: /static[\\/]adapt-pc[\\/]/,
                use: 'null-loader'
            })

            // 添加压缩配置
            if (config.optimization.minimizer) {
                config.optimization.minimizer.forEach(minimizer => {
                    if (minimizer.constructor.name === 'TerserPlugin') {
                        minimizer.options.terserOptions = {
                            ...minimizer.options.terserOptions,
                            compress: {
                                ...minimizer.options.terserOptions.compress,
                                drop_console: true, // 移除console
                                drop_debugger: true, // 移除debugger
                                pure_funcs: ['console.log', 'console.info', 'console.debug'] // 移除指定函数
                            }
                        }
                    }
                })
            }
        }
    },

    chainWebpack: config => {
        // 针对微信小程序的链式配置
        if (process.env.UNI_PLATFORM === 'mp-weixin') {
            // 优化模块解析
            config.resolve.alias
                .set('@', path.resolve(__dirname, 'src'))
                .set('components', path.resolve(__dirname, 'src/components'))
                .set('common', path.resolve(__dirname, 'src/common'))
                .set('utils', path.resolve(__dirname, 'src/utils'))
                .set('static', path.resolve(__dirname, 'src/static'))
        }
    }
}
