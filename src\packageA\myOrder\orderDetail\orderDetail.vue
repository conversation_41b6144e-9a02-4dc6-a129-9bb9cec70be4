<!-- 订单详情 -->
<template>
  <view class="goodsinfo">
    <view class="addr d-cf" v-if="orderDetail.shipping_address">
      <view class="d-cf">
        <view class="iconfont icon-dingwei"></view>
      </view>
      <view>
        <view>
          收件人：{{
            orderDetail.shipping_address &&
            orderDetail.shipping_address.realname
          }}
          {{
            orderDetail.shipping_address && orderDetail.shipping_address.mobile
          }}
        </view>
        <view>
          {{
            orderDetail.shipping_address &&
            orderDetail.shipping_address.province
          }}
          {{
            orderDetail.shipping_address && orderDetail.shipping_address.city
          }}
          {{
            orderDetail.shipping_address && orderDetail.shipping_address.county
          }}
          {{
            orderDetail.shipping_address && orderDetail.shipping_address.town
          }}
          {{
            orderDetail.shipping_address && orderDetail.shipping_address.detail
          }}
        </view>
      </view>
    </view>
    <view class="addr" v-else>
      <view class="d-cf">
        <view class="iconfont icon-dingwei"></view>
        <view>无需收货地址 如信息出错请联系客服！</view>
      </view>
      <view class="copy-box d-c mt_24">
        <view class="copy-address d-cc">
          <view class="iconfont icon-service_n"></view>
          <view>复制地址</view>
        </view>
      </view>
    </view>

    <!--商品名字和信息 -->
    <view class="goods-box">
      <block
        v-for="(goodsItem, goodsindex) in orderDetail.order_items"
        :key="goodsindex"
      >
        <view
          class="goods d-f"
          @click="
            navTo(
              '/packageA/commodity/commodity_details/commodity_details?id=' +
                goodsItem.product_id,
            )
          "
        >
          <view class="img mr_32">
            <image :src="goodsItem.image_url"></image>
          </view>
          <view class="goods_right d-be">
            <view>
              <u--text
                :lines="2"
                color="#333"
                size="28"
                lineHeight="36rpx"
                :text="goodsItem.title"
              ></u--text>
              <view class="d-bf">
                <view>规格：{{ goodsItem.sku_title }}</view>
                <view class="c-888 mt_20">x {{ goodsItem.qty }}</view>
              </view>
            </view>
            <view class="d-ef">
              ￥{{ goodsItem.amount ? goodsItem.amount : '0.00' }}
            </view>
          </view>
        </view>
      </block>
    </view>
    <!-- 订单单号 -->
    <view class="tbs">
      <view class="tbs-number-tra">
        <view class="number d-bf2">
          <view>订单编号：</view>
          <view>{{ orderDetail.order_sn }}</view>
        </view>
        <view class="number d-bf2">
          <view>交易状态：</view>
          <view>{{ orderDetail.status_name }}</view>
        </view>
      </view>
      <view class="Pay-method">
        <view class="method d-bf2" v-if="orderDetail.status !== 0">
          <view>支付方式:</view>
          <view class="c-orange">{{ orderDetail.pay_type }}</view>
        </view>
        <view class="mb40"></view>
        <view class="number d-bf2">
          <view>运费:</view>

          <view>¥{{ orderDetail.freight ? orderDetail.freight : '0.00' }}</view>
        </view>
      </view>

      <view class="Pay-method">
        <block
          v-for="(DetailItem, DetailIndex) in amount_items"
          :key="DetailIndex"
        >
          <view class="number d-bf2">
            <view>{{ DetailItem.title }}</view>
            <view>¥{{ DetailItem.amount }}</view>
          </view>
        </block>
      </view>
      <view class="Pay-method">
        <view class="mt_10 mb_20">买家留言:</view>
        <view>{{ orderDetail.remark }}</view>
      </view>
      <view class="real_pay">
        <view class="font_size16 c-333 actually d-ef">
          实付款:
          <text class="font_size18 c-orange ml_10">
            <text class="font_size12">￥</text>
            {{ orderDetail.amount ? orderDetail.amount : '0.00' }}
          </text>
        </view>
        <view class="order_times d-bf2 mt_28" v-if="orderDetail.paid_at">
          <view>创建时间:</view>
          <view>{{ orderDetail.created_at }}</view>
        </view>
        <view
          class="order_times d-bf2"
          v-if="orderDetail.paid_at && orderDetail.status !== 0"
        >
          <view>付款时间:</view>
          <view>{{ orderDetail.paid_at }}</view>
        </view>
        <view
          class="order_times d-bf2"
          v-if="
            (orderDetail.sent_at && orderDetail.status === 2) ||
            orderDetail.status === 3
          "
        >
          <view>发货时间:</view>
          <view>{{ orderDetail.sent_at }}</view>
        </view>
        <view
          v-if="addressService"
          class="service d-f"
          @click="externalLinkJudgment(addressService)"
        >
          <view class="iconfont icon-kefu"></view>
          <view>联系客服</view>
        </view>
      </view>
    </view>
    <!-- <view class="addr agent-box f fac fjsb">
      <view>来源代理商</view>
      <view>[52221532]卡卡</view>
    </view> -->

    <view class="bottomOf"></view>
    <view class="mb96"></view>
    <!-- 支付订单按钮 -->
    <view class="detail_pay d-ef">
      <block
        v-for="(itemBtn, indexBtn) in orderDetail.operations"
        :key="indexBtn"
      >
        <view
          class="order_delete"
          v-if="
            itemBtn.code !== 'bill' ||
            (orderDetail.order_bill && orderDetail.order_bill.bill_id > 0)
          "
          @click="
            orderBtn(
              itemBtn.code,
              orderDetail.order_items,
              goosAmount,
              orderDetail.order_bill,
            )
          "
        >
          {{ itemBtn.title }}
        </view>
      </block>
    </view>

    <u-modal
      width="300px"
      :content="hintContent"
      :showCancelButton="true"
      :show="orderShow"
      @cancel="orderCancel('order')"
      @confirm="orderConfirm('order')"
    ></u-modal>
    <u-modal
      width="300px"
      :content="receiveContent"
      :showCancelButton="true"
      :show="receiveShow"
      @cancel="orderCancel('receive')"
      @confirm="orderConfirm('receive')"
    ></u-modal>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: 0,
      orderDetail: {},
      amount_items: [],
      orderShow: false,
      receiveShow: false,
      hintContent: '是否取消订单',
      receiveContent: '是否确认收货',
      orderUrl: '', //后台api接口
      goosAmount: 0,
      addressService: '', //客服跳转地址
    }
  },
  onLoad(options) {
    if ((options.order_id ?? '') !== '') {
      this.id = parseInt(options.order_id)
    }
    this.detail(this.id)
    this.framework()
  },
  onShow() {
    // this.detail(this.id);
  },
  methods: {
    detail(id) {
      this.get(`/api/order/get?id=${id}`, {}, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.orderDetail = data.order
            this.orderDetail.amount = this.toYuan(data.order.amount)
            this.orderDetail.order_goods_price = this.toYuan(
              data.order.order_goods_price,
            )
            this.orderDetail.freight = this.toYuan(data.order.freight)
            this.orderDetail.created_at = this.formatDateTime(
              this.orderDetail.created_at,
              6,
            )
            this.orderDetail.paid_at = this.formatDateTime(
              this.orderDetail.paid_at,
              6,
            )

            this.orderDetail.sent_at = this.formatDateTime(
              this.orderDetail.sent_at,
              6,
            )
            this.amount_items = this.orderDetail.amount_detail.amount_items
            let order_items = this.orderDetail.order_items
            for (let j = 0; j < this.amount_items.length; j++) {
              this.amount_items[j].amount = this.toYuan(
                this.amount_items[j].amount,
              )
            }
            for (let i = 0; i < order_items.length; i++) {
              order_items[i].amount = this.toYuan(order_items[i].amount)
              this.goosAmount = order_items[i].amount
            }
            for (let k = 0; k < this.orderDetail.operations.length; k++) {
              //处理后端返回的按钮,售后状态信息
              if (
                this.orderDetail.order_items.length == 1 &&
                this.orderDetail.operations[k].code === 'refund' &&
                this.orderDetail.order_items[0].refund_status === 2
              ) {
                this.orderDetail.operations[k].title = '售后中'
              }
            }
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    orderCancel(type) {
      if (type === 'order') {
        this.orderShow = false
      } else {
        this.receiveShow = false
      }
    },
    framework() {
      //获取客服的跳转链接
      this.get('/api/home/<USER>', {}, true).then(data => {
        this.addressService = data.data.footer.service_link
      })
    },
    orderBtn(code, orderItems, refund, order_bill) {
      //订单的按钮事件
      if (code === 'close') {
        this.orderShow = true
      } else if (code === 'pay') {
        console.log(code)
        if (this.id) {
          let orderId = []
          orderId.push(this.id)
          uni.setStorageSync('orderIds', orderId)
          uni.navigateTo({
            url: '/packageA/goodsorder/orderpay/orderpay',
          })
        }
      } else if (code === 'refund') {
        if (orderItems.length > 1) {
          uni.navigateTo({
            url: `/packageB/member/evaluateList?refundId=${this.id}`,
          })
        } else {
          if (orderItems[0].can_refund) {
            //可以退款（1是0否）
            if (orderItems[0].refund_status === 2) {
              uni.navigateTo({
                url: `/packageB/member/aftersales?afterSaleId=${orderItems[0].id}&order=order`,
              })
            } else {
              uni.navigateTo({
                url: `/packageB/member/refund?id=${orderItems[0].id}`,
              })
            }
          } else {
            this.toast('订单不支持退款')
          }
        }
      } else if (code === 'express_info') {
        //物流信息
        let image_url = orderItems.length ? orderItems[0].image_url : ''
        uni.navigateTo({
          url:
            '/packageB/member/logistics?id=' +
            this.id +
            '&image_url=' +
            image_url,
        })
      } else if (code === 'receive') {
        //确认收货
        this.receiveShow = true
      } else if (code === 'bill') {
        //发票信息
        uni.navigateTo({
          url:
            '/packageC/invoiceDetails/invoiceDetails?id=' + order_bill.bill_id,
        })
      } else {
      }
    },
    orderConfirm(type) {
      if (type === 'receive') {
        this.orderUrl = `/api/order/receive?order_id=${this.id}`
      } else {
        this.orderUrl = `/api/order/close?order_id=${this.id}`
      }
      this.get(this.orderUrl, {}, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.orderShow = false
            this.receiveShow = false
            this.detail(this.id)
            setTimeout(() => {
              this.toast(res.msg)
            }, 1000)

            if (this.tabStatus == '') {
              this.order_goods = []
              this.orderListTabStatus()
            } else {
              this.order_goods = []
              this.orderList()
            }
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
#goodsinfo {
  min-height: 110vh;
}
.addr {
  margin: 20rpx;
  padding: 24rpx;
  background-color: #fff;
  .icon-dingwei {
    margin: 2rpx 20rpx 0 0;
  }
  .copy-address {
    background-color: #fff;
    color: #ff5f5f;
    border: 1px solid #ff5f5f;
    width: 188rpx;
    height: 60rpx;
    border-radius: 30rpx;
  }
}
.goods-box {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx;
  padding-bottom: 20rpx;
  .goods {
    padding: 20rpx 20rpx 20rpx 32rpx;
    .img {
      image {
        width: 140rpx;
        height: 140rpx;
      }
    }
    .goods_right {
      width: 100%;
    }
  }
}
.tbs {
  margin: 20rpx 20rpx 20rpx 20rpx;
  border-radius: 20rpx;
  background-color: #fff;
  .number {
    line-height: 60rpx;
  }
  .tbs-number-tra {
    padding: 20rpx 28rpx;
    border-bottom: 2rpx solid #ebebeb;
  }
  .Pay-method {
    padding: 0rpx 28rpx 20rpx 28rpx;
    border-bottom: 2rpx solid #ebebeb;
    .method {
      line-height: 72rpx;
    }
  }
  .real_pay {
    padding: 0rpx 28rpx 20rpx 28rpx;
    .actually {
      line-height: 72rpx;
    }
    .order_times {
      line-height: 60rpx;
      // padding: 16rpx 0;
    }
  }
  .service {
    .icon-kefu {
      font-size: 20px;
      color: #f15353;
      margin-right: 6rpx;
    }
  }
}
.detail_pay {
  width: 100%;
  box-sizing: border-box;
  min-height: 96rpx;
  background: #fff;
  padding: 0 24rpx;
  border-top: 1px solid #ebebeb;
  position: fixed;
  bottom: 0;
  .order_delete {
    min-width: 130rpx;
    width: auto;
    margin-left: 16rpx;
    text-align: center;
    background: #fff;
    padding: 8rpx 20rpx;
    border-radius: 15px;
    color: #333;
    border: 2rpx solid #666;
  }
  .red_button {
    border: 2rpx solid #f14e4e;
    background-color: #f14e4e;
    color: #fff;
  }
}
.bottomOf {
  margin: -80rpx 20rpx 112rpx 20rpx;
}
.agent-box {
  margin-bottom: 158rpx;
  border-radius: 16rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #202020;
}
</style>
