<template>
  <view class="agent-box">
    <u-form :model="fromData" ref="fromData">
      <view v-if="status === 2" class="form-box">
        <view class="title black">驳回理由</view>
        <view class="black mt_20">{{ reject_reason }}</view>
        <view class="tip mt_24">注：您的申请已被驳回，请修改后重新提交！</view>
      </view>
      <view class="form-box" :class="status === 2 ? 'mt_20' : null">
        <view class="f fac">
          <view class="red mr_5">*</view>
          <u-form-item label="申请等级" labelWidth="170rpx">
            <view class="f fac fjsb w100" @click="applyLevelShow">
              <view
                class="input-view-name1"
                v-if="fromData.applyLevel === '请选择'"
              >
                {{ fromData.applyLevel }}
              </view>
              <view class="input-view-name" v-else>
                {{ fromData.applyLevel }}
              </view>
              <view
                class="iconfont icon-member_right icon_aaa"
                style="color: #aaaab3"
              ></view>
            </view>
          </u-form-item>
        </view>
      </view>
      <view
        v-if="register_base_setting.open_register_info !== 0"
        class="form-box mt_20"
      >
        <view
          v-if="register_base_setting.full_name.show_on_front === 1"
          class="f fac"
        >
          <view
            v-if="register_base_setting.full_name.must_fill === 1"
            class="red mr_5"
          >
            *
          </view>
          <u-form-item label="姓名" labelWidth="170rpx" borderBottom>
            <u--input
              style="width: 440rpx"
              v-model="fromData.username"
              placeholder="请输入"
              border="none"
            ></u--input>
          </u-form-item>
        </view>
        <view
          v-if="register_base_setting.mobile.show_on_front === 1"
          class="f fac"
        >
          <view
            v-if="register_base_setting.mobile.must_fill === 1"
            class="red mr_5"
          >
            *
          </view>
          <u-form-item label="电话" labelWidth="170rpx" borderBottom>
            <u--input
              style="width: 440rpx"
              v-model="fromData.mobile"
              placeholder="请输入"
              border="none"
            ></u--input>
          </u-form-item>
        </view>
        <view
          v-if="register_base_setting.address.show_on_front === 1"
          class="f fac"
        >
          <view
            v-if="register_base_setting.address.must_fill === 1"
            class="red mr_5"
          >
            *
          </view>
          <u-form-item label="用户地址" labelWidth="170rpx" borderBottom>
            <view class="f fac fjsb w100" @click="openDateLwtbtn">
              <view
                class="input-view-name1"
                v-if="fromData.chooseAddress === '请选择'"
              >
                {{ fromData.chooseAddress }}
              </view>
              <view class="input-view-name" v-else>
                {{ fromData.chooseAddress }}
              </view>
              <view
                class="iconfont icon-member_right icon_aaa"
                style="color: #aaaab3"
              ></view>
            </view>
          </u-form-item>
        </view>
        <view
          v-if="register_base_setting.address.show_on_front === 1 && streetItemShow"
          class="f fac"
        >
          <view
            v-if="register_base_setting.address.must_fill === 1"
            class="red mr_5"
          >
            *
          </view>
          <u-form-item label="街道" labelWidth="170rpx" borderBottom>
            <view class="f fac fjsb w100" @click="streetBtn">
              <view
                class="input-view-name1"
                v-if="fromData.chooseStreet === '请选择'"
              >
                {{ fromData.chooseStreet }}
              </view>
              <view class="input-view-name" v-else>
                {{ fromData.chooseStreet }}
              </view>
              <view
                class="iconfont icon-member_right icon_aaa"
                style="color: #aaaab3"
              ></view>
            </view>
          </u-form-item>
        </view>
        <view
          v-if="register_base_setting.address.show_on_front === 1"
          class="f fac"
        >
          <view
            v-if="register_base_setting.address.must_fill === 1"
            class="red mr_5"
          >
            *
          </view>
          <u-form-item label="详细地址" labelWidth="170rpx" borderBottom>
            <u--input
              style="width: 440rpx"
              v-model="fromData.address"
              placeholder="请输入"
              border="none"
            ></u--input>
          </u-form-item>
        </view>
        <view
          v-if="register_base_setting.wechat_qr_code.show_on_front === 1"
          class="f fac"
        >
          <view
            v-if="register_base_setting.wechat_qr_code.must_fill === 1"
            class="red mr_5"
          >
            *
          </view>
          <u-form-item label="微信二维码" labelWidth="170rpx"></u-form-item>
        </view>
        <u-upload
          v-if="register_base_setting.wechat_qr_code.show_on_front === 1"
          accept="image"
          :fileList="fromData.wechat_qr_code"
          @afterRead="afterWeChatCode"
          :previewFullImage="true"
          @delete="deleteWeChatCode"
          width="204rpx"
          height="204rpx"
          uploadIcon="plus"
          :maxCount="1"
        ></u-upload>
        <view
          v-if="register_base_setting.alipay_qr_code.show_on_front === 1"
          class="f fac"
        >
          <view
            v-if="register_base_setting.alipay_qr_code.must_fill === 1"
            class="red mr_5"
          >
            *
          </view>
          <u-form-item label="支付宝收款码" labelWidth="190rpx"></u-form-item>
        </view>
        <u-upload
          accept="image"
          v-if="register_base_setting.alipay_qr_code.show_on_front === 1"
          :fileList="fromData.alipay_pay_code"
          @afterRead="afterAlipayCode"
          :previewFullImage="true"
          @delete="deleteAlipayCode"
          width="204rpx"
          height="204rpx"
          uploadIcon="plus"
          :maxCount="1"
        ></u-upload>
        <u-form-item
          v-if="register_base_setting.alipay_qr_code.show_on_front === 1"
          borderBottom
        ></u-form-item>
        <view
          v-if="register_base_setting.bank_account.show_on_front === 1"
          class="f fac"
        >
          <view
            v-if="register_base_setting.bank_account.must_fill === 1"
            class="red mr_5"
          >
            *
          </view>
          <u-form-item label="银行卡收款账号" labelWidth="170rpx" borderBottom>
            <view class="f fac fjsb w100" @click="chooseBank">
              <view class="input-view-name1" v-if="fromData.bank === '请选择'">
                {{ fromData.bank }}
              </view>
              <view class="input-view-name" v-else>{{ fromData.bank }}</view>
              <view
                class="iconfont icon-member_right icon_aaa"
                style="color: #aaaab3"
              ></view>
            </view>
          </u-form-item>
        </view>
        <view
          v-if="register_base_setting.wechat_pay_qr_code.show_on_front === 1"
          class="f fac"
        >
          <view
            v-if="register_base_setting.wechat_pay_qr_code.must_fill === 1"
            class="red mr_5"
          >
            *
          </view>
          <u-form-item label="微信收款码" labelWidth="170rpx"></u-form-item>
        </view>
        <u-upload
          accept="image"
          v-if="register_base_setting.wechat_pay_qr_code.show_on_front === 1"
          :fileList="fromData.wechat_pay_code"
          @afterRead="afterWeChatPayCode"
          :previewFullImage="true"
          @delete="deleteWeChatPayCode"
          width="204rpx"
          height="204rpx"
          uploadIcon="plus"
          :maxCount="1"
        ></u-upload>
      </view>
      <view v-if="formList.length !== 0" class="form-box mt_20">
        <view class="f fac" v-for="(item, index) in formList" :key="index">
          <view v-if="item.required" class="red mr_5">*</view>
          <u-form-item :label="item.label" labelWidth="170rpx" borderBottom>
            <u--input
              style="width: 440rpx"
              v-if="item.type === 'text'"
              :placeholder="
                item.setting.placeholder
                  ? item.setting.placeholder
                  : '请输入内容'
              "
              v-model="item.value"
              border="none"
            ></u--input>
            <u-upload
              style="width: 440rpx"
              v-if="item.type === 'images'"
              accept="image"
              :fileList="formList[index].value"
              @afterRead="
                afterCustomForm($event, item.setting.max_count, index)
              "
              :previewFullImage="true"
              @delete="deleteCustomForm($event, index)"
              width="180rpx"
              height="180rpx"
              uploadIcon="plus"
              :maxCount="item.setting.max_count"
            ></u-upload>
          </u-form-item>
        </view>
      </view>
      <view class="mt_40 pb_150">
        <u-button
          :customStyle="{ color: '#ffffff',  backgroundColor: '#f15353'}"
          style="color: #fff; background-color: #f15353"
          shape="circle"
          text="提交"
          @click="submit"
        ></u-button>
      </view>
    </u-form>
    <u-popup :show="levelshow" :round="40" mode="bottom">
      <view
        class="pl_20 pt_60 pr_20"
        style="min-height: 500rpx; color: #00001c"
      >
        <view class="f fjsb">
          <text class="fs_30" @click="levelshow = false">取消</text>
          <text class="fs_30" @click="levelshow = false">确定</text>
        </view>
        <view class="level_list mt_30">
          <u-radio-group v-model="fromData.level_id" iconPlacement="right">
            <u-radio
              class="mt_40"
              :customStyle="{ marginTop: '40rpx' }"
              activeColor="#F15353"
              v-for="item in levelList"
              :key="item.id"
              :label="item.level_name"
              :name="item.id"
              @change="radioChange(item)"
            ></u-radio>
          </u-radio-group>
        </view>
      </view>
    </u-popup>
    <!-- 选择地址的UI组件 -->
    <address-popup
      :addressShow="addressShow"
      @_closeDateLw="closeDateLw"
      @AddressSetOn="addressSetOn"
    ></address-popup>
    <!-- 选择街道组件 -->
    <street-popup
      ref="streetPopup"
      :streetObj="streetObj"
      @closeStreet="closeStreet"
      @streetConfirm="streetConfirm"
    ></street-popup>
    <!-- 银行卡 -->
    <u-popup :show="show" @close="close" :round="20">
      <view class="main">
        <view class="header d-c">
          <view class="bank_title">选择银行卡</view>

          <view class="iconfont icon-close11 title_close" @click="close"></view>
        </view>
        <view class="content">
          <block v-for="(item, index) in cardList" :key="index">
            <view
              class="bank-item"
              @click="bankBtn(item.bank_account, item.id)"
            >
              <view>{{ item.bank_name }}</view>
              <view>{{ item.bank_account }}</view>
              <view class="c-74">{{ item.account_name }}</view>
            </view>
          </block>
          <view class="mb20"></view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import addressPopup from '@/components/addressPopup.vue'
import streetPopup from '@/components/streetPopup.vue'
export default {
  name: 'applyAgent',
  components: {
    addressPopup,
    streetPopup,
  },
  data() {
    return {
      streetItemShow:false, // 显示街道的item
      show: false,
      addressShow: false,
      streetObj: {
        //传递给街道组件
        show: false,
        countyId: 0,
      },
      status: 1, // 驳回状态
      reject_reason: '', // 驳回理由
      fromData: {
        applyLevel: '请选择', // 选择等级
        level_id: '', // 等级id
        username: '', // 姓名
        mobile: '', // 电话
        province: '', // 省
        province_id: null, // 省ID
        city: '', // 市
        city_id: null, // 市ID
        district: '', // 区
        district_id: null, // 区ID
        street: '', // 街道
        street_id: null, // 街道ID
        chooseAddress: '请选择', // 地址
        chooseStreet: '请选择', // 街道
        address: '', // 详细地址
        bank: '请选择', // 银行卡
        bank_account: '', // 银行卡号
        wechat_qr_code: [], // 微信二维码
        alipay_pay_code: [], // 支付宝收款码
        wechat_pay_code: [], // 微信收款码
      },
      cardList: [], // 银行卡列表
      levelshow: false, // 等级弹出层
      levelList: [], // 等级列表
      register_base_setting: {
        address: { must_fill: 1, show_on_front: 1 },
        alipay_qr_code: { must_fill: 1, show_on_front: 1 },
        bank_account: { must_fill: 1, show_on_front: 1 },
        full_name: { must_fill: 1, show_on_front: 1 },
        mobile: { must_fill: 1, show_on_front: 1 },
        wechat_pay_qr_code: { must_fill: 1, show_on_front: 1 },
        wechat_qr_code: { must_fill: 1, show_on_front: 1 },
        open_register_info: 0,
      },
      formList: [], // 自定义表单
    }
  },
  onLoad(options) {
    this.getAgentSetting() // 基础设置
    this.getUserBankList() //银行卡列表
    this.getAgentLevel() // 获取等级
    this.getAgentApplyDetail() // 获取信息
  },
  onShow() {
    
  },
  methods: {
    // 获取代理商基础设置
    async getAgentSetting() {
      const res = await this.get('/api/agent/setting/get')
      if (res.code === 0) {
        this.register_base_setting =
          res.data.setting.value.register_base_setting
        let fields = res.data.form.fields
          ? JSON.parse(res.data.form.fields)
          : []
        fields.forEach(element => {
          if (element.type === 'images') {
            element.value = []
          } else {
            element.value = ''
          }
        })
        this.formList = fields
        console.log('wqewqewqe', fields)
      }
    },
    // 等级弹层
    applyLevelShow() {      
      this.levelshow = true
    },
    // 打开省市区地址
    openDateLwtbtn() {
      this.addressShow = true
      this.fromData.street = ''
    },
    // 选择省市区地址
    addressSetOn(...query) {
      this.fromData.chooseAddress = query[1]
      this.addressShow = false
      this.fromData.city = query[0].city
      this.fromData.city_id = query[0].city_id
      this.fromData.district = query[0].county
      this.fromData.district_id = query[0].county_id
      this.fromData.province = query[0].province
      this.fromData.province_id = query[0].province_id
      if (this.fromData.district_id) {
        this.streetObj.countyId = this.fromData.district_id
        this.streetItemShow = true;
      }
      this.$refs.streetPopup.streetListData(this.fromData.district_id)
      this.fromData.street = ''
      this.fromData.street_id = ''
      this.fromData.chooseStreet = '请选择'
    },
    // 关闭选择地址
    closeDateLw(cancel) {
      this.addressShow = !cancel
    },
    // 打开街道地址
    streetBtn() {
      this.streetObj.show = true
    },
    //获取街道地址
    streetConfirm(street) {
      this.fromData.street = street.town
      this.fromData.street_id = street.town_id
      this.fromData.chooseStreet = street.town
    },
    // 关闭街道弹层
    closeStreet(show) {
      this.streetObj.show = false
    },
    // 图片上传
    async afterWeChatCode(event) {
      this.fromData.wechat_qr_code = [
        {
          url: event.file.url,
          name: event.file.name,
        },
      ]
      this.uploadFilePromise(
        this.fromData.wechat_qr_code[0].url,
        'wechat_qr_code',
      )
    },
    // 删除图片
    deleteWeChatCode() {
      this.fromData.wechat_qr_code = []
    },
    // 图片上传
    async afterAlipayCode(event) {
      this.fromData.alipay_pay_code = [
        {
          url: event.file.url,
          name: event.file.name,
        },
      ]
      this.uploadFilePromise(
        this.fromData.alipay_pay_code[0].url,
        'alipay_pay_code',
      )
    },
    // 删除图片
    deleteAlipayCode() {
      this.fromData.alipay_pay_code = []
    },
    // 图片上传
    afterWeChatPayCode(event) {
      this.fromData.wechat_pay_code = [
        {
          url: event.file.url,
          name: event.file.name,
        },
      ]
      this.uploadFilePromise(
        this.fromData.wechat_pay_code[0].url,
        'wechat_pay_code',
      )
    },
    // 删除图片
    deleteWeChatPayCode() {
      this.fromData.wechat_pay_code = []
    },
    // 上传自定义图片
    afterCustomForm(event, count, index) {
      this.formList[index].value.push({
        url: event.file.url,
        name: event.file.name,
      })
      this.uploadFilePromise(
        this.formList[index].value[event.index].url,
        'custom_upload_image',
        index,
        event.index,
      )
    },
    // 删除自定义图片
    deleteCustomForm(event, index) {
      this.formList[index].value.splice(event.index, 1)
    },
    // 打开银行卡
    chooseBank() {
      if (this.cardList.length === 0) {
        this.toast('请绑定银行卡')
        setTimeout(() => {
          this.navTo('/packageB/member/bindBank')
        }, 500)
      } else {
        this.show = true
      }
    },
    // 关闭银行卡弹层
    close() {
      this.show = false
    },
    //获取银行卡列表
    getUserBankList() {
      this.post('/api/finance/getUserBankList', {}, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.cardList = data
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {
          console.log(Error)
        })
    },
    // 选择银行卡
    bankBtn(account, id) {
      this.fromData.bank_account = account
      this.fromData.bank = account
      this.show = false
    },
    // 获取等级列表
    async getAgentLevel() {
      const res = await this.post('/api/agent/level/select')
      if (res.code === 0) {
        this.levelList = res.data
      }
    },
    // 选择等级
    radioChange(item) {
      this.fromData.applyLevel = item.level_name
      this.fromData.level_id = item.id
    },
    // 提交申请
    async submit() {
      if (!this.fromData.level_id) {
        this.toast('请选择等级')
        return
      }
      if (
        this.register_base_setting.full_name.must_fill === 1 &&
        this.register_base_setting.full_name.show_on_front === 1 &&
        this.register_base_setting.open_register_info !== 0 &&
        !this.fromData.username
      ) {
        this.toast('请填写姓名')
        return
      }
      if (
        this.register_base_setting.mobile.must_fill === 1 &&
        this.register_base_setting.mobile.show_on_front === 1 &&
        this.register_base_setting.open_register_info !== 0 &&
        !this.fromData.mobile
      ) {
        this.toast('请填写电话')
        return
      }
      if (
        this.register_base_setting.address.must_fill === 1 &&
        this.register_base_setting.address.show_on_front === 1 &&
        this.register_base_setting.open_register_info !== 0 &&
        !this.fromData.province_id &&
        !this.fromData.city_id &&
        !this.fromData.district_id
      ) {
        this.toast('请选择用户地址')
        return
      }
      if (
        this.register_base_setting.address.must_fill === 1 &&
        this.register_base_setting.address.show_on_front === 1 &&
        this.register_base_setting.open_register_info !== 0 &&
        !this.fromData.street_id
      ) {
        this.toast('请选择街道')
        return
      }
      if (
        this.register_base_setting.address.must_fill === 1 &&
        this.register_base_setting.address.show_on_front === 1 &&
        this.register_base_setting.open_register_info !== 0 &&
        !this.fromData.address
      ) {
        this.toast('请填写地址')
        return
      }
      if (
        this.register_base_setting.wechat_qr_code.must_fill === 1 &&
        this.register_base_setting.wechat_qr_code.show_on_front === 1 &&
        this.register_base_setting.open_register_info !== 0 &&
        this.fromData.wechat_qr_code.length === 0
      ) {
        this.toast('请上传微信二维码')
        return
      }
      if (
        this.register_base_setting.alipay_qr_code.must_fill === 1 &&
        this.register_base_setting.alipay_qr_code.show_on_front === 1 &&
        this.register_base_setting.open_register_info !== 0 &&
        this.fromData.alipay_pay_code.length === 0
      ) {
        this.toast('请上传支付宝收款码')
        return
      }
      if (
        this.register_base_setting.bank_account.must_fill === 1 &&
        this.register_base_setting.bank_account.show_on_front === 1 &&
        this.register_base_setting.open_register_info !== 0 &&
        !this.fromData.bank_account
      ) {
        this.toast('请选择银行卡')
        return
      }
      if (
        this.register_base_setting.wechat_pay_qr_code.must_fill === 1 &&
        this.register_base_setting.wechat_pay_qr_code.show_on_front === 1 &&
        this.register_base_setting.open_register_info !== 0 &&
        this.fromData.wechat_pay_code.length === 0
      ) {
        this.toast('请上传微信收款码')
        return
      }
      function validateFormList(formList) {
        for (let item of formList) {
          if (item.required && item.value.length === 0) {
            return { isValid: false, type: item.type }
          }
        }
        // 如果没有遇到不符合条件的，返回 true
        return { isValid: true }
      }
      const result = validateFormList(this.formList)
      if (!result.isValid && result.type === 'images') {
        this.toast('请上传图片')
        return
      }
      if (!result.isValid && result.type === 'text') {
        this.toast('请输入内容')
        return
      }
      let res = ''
      let data = {
        ...this.fromData,
      }
      data.wechat_qr_code =
        this.fromData.wechat_qr_code.length !== 0
          ? this.fromData.wechat_qr_code[0].url
          : ''
      data.wechat_pay_code =
        this.fromData.wechat_pay_code.length !== 0
          ? this.fromData.wechat_pay_code[0].url
          : ''
      data.alipay_pay_code =
        this.fromData.alipay_pay_code.length !== 0
          ? this.fromData.alipay_pay_code[0].url
          : ''
      if (this.formList.length !== 0) {
        data.fields_data = JSON.stringify(this.formList)
      }
      if (this.status === 2) {
        res = await this.post('/api/agent/apply/update', data)
      } else {
        res = await this.post('/api/agent/apply/create', data)
      }
      if (res.code === 0) {
        this.toast(res.msg)
      }
      setTimeout(() => {
        this.navTo('/pages/membercenter/membercenter')
      }, 1000)
    },
    async getAgentApplyDetail() {
      this.post('/api/agent/apply/detail',{},false,false,false).then((res) => {
          if (res.code === 0) {
            this.status = res.data.detail.status
            this.reject_reason = res.data.detail.reject_reason
            this.fromData = res.data.detail
            this.fromData.applyLevel = res.data.detail.agent_level.level_name
            this.fromData.level_id = res.data.detail.agent_level.id;
            // 图片回显
            if (res.data.detail.wechat_qr_code) {
              this.fromData.wechat_qr_code = [
                {
                  url: res.data.detail.wechat_qr_code,
                },
              ]
            };
            if (res.data.detail.wechat_pay_code) {
              this.fromData.wechat_pay_code = [
                {
                  url: res.data.detail.wechat_pay_code,
                },
              ]
            };
            if (res.data.detail.alipay_pay_code) {
              this.fromData.alipay_pay_code = [
                {
                  url: res.data.detail.alipay_pay_code,
                },
              ]
            };
            this.fromData.bank = res.data.detail.bank_account
            let province_name = res.data.detail.province
              ? res.data.detail.province.name
              : ''
            let district_name = res.data.detail.district
              ? res.data.detail.district.name
              : ''
            let city_name = res.data.detail.city ? res.data.detail.city.name : ''
            this.fromData.chooseAddress = province_name + district_name +city_name
            this.fromData.chooseStreet = res.data.detail.street
              ? res.data.detail.street.name
              : ''
            this.$refs.streetPopup.streetListData(res.data.detail.district_id)
            // this.formList = res.data.detail.fields_data ? JSON.parse(res.data.detail.fields_data) : []
            if (res.data.detail.fields_data) {
              this.formList = JSON.parse(res.data.detail.fields_data)
            }
          }
      }).catch(Error => {        
      })
    },
    // 上传方法
    uploadFilePromise(url, file, index, eventIndex) {
      return new Promise((resolve, reject) => {
        const a = uni.uploadFile({
          url: this.api.host + '/api/common/upload',
          filePath: url,
          name: 'file',
          success: res => {
            setTimeout(() => {
              resolve(res.data.data)
              const data = JSON.parse(res.data)
              switch (file) {
                case 'wechat_pay_code':
                  this.fromData.wechat_pay_code[0].url = data.data.file.url
                  break
                case 'alipay_pay_code':
                  this.fromData.alipay_pay_code[0].url = data.data.file.url
                  break
                case 'wechat_qr_code':
                  this.fromData.wechat_qr_code[0].url = data.data.file.url
                  break
                case 'custom_upload_image':
                  this.formList[index].value[eventIndex].url =
                    data.data.file.url
                  break
                default:
                  break
              }
              // this.toastName[file.split('fileList_')[1]] = true
            })
          },
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.agent-box {
  width: 702rpx;
  margin-top: 20rpx;
  margin-left: 24rpx;
  margin-right: 24rpx;
  border-radius: 16rpx;
  .form-box {
    background: #ffffff;
    border-radius: 16rpx;
    width: 702rpx;
    padding: 14rpx 32rpx;
    box-sizing: border-box;
  }
}
.main {
  width: 100%;
  height: 709rpx;
  border-radius: 20rpx;
  margin: 0 auto 10rpx auto;
  background: #fff;
  .header {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    position: relative;
    .title_close {
      position: absolute;
      right: 30rpx;
    }
  }
  .content {
    height: 629rpx;
    margin: 0 30rpx 0 30rpx;
    overflow-y: scroll;
    .bank-item {
      font-size: 24rpx;
      view {
        margin-bottom: 24rpx;
      }
    }
  }
}
.bank_title {
  color: #202020;
  font-size: 28rpx;
  margin-right: 105rpx;
}
.w100 {
  width: 440rpx;
}
.input-view-name {
  font-size: 30rpx;
}
.input-view-name1 {
  font-size: 30rpx;
  color: rgb(192, 196, 204);
}
.tip {
  color: #f14e4e;
  font-size: 26rpx;
}
.black {
  color: #202020;
  font-size: 26rpx;
}
.red {
  color: #f14e4e;
}
.title {
  font-weight: bold;
  font-size: 28rpx;
}
.fs_30 {
  font-size: 30rpx;
}
::v-deep .u-radio__icon-wrap--circle {
  width: 32rpx !important;
  height: 32rpx !important;
}
::v-deep .u-radio-group--row {
  flex-direction: column !important;
}
::v-deep .u-radio__text {
  color: #00001c !important;
  font-size: 28rpx !important;
}
</style>
