<template>
    <view>
        <view class="search-box">
            <u-search
                height="64rpx"
                placeholder="请输入会员/手机号"
                clearabled
                :showAction="false"
                searchIconSize="40"
                v-model="user_kwd"
                @change="search"
            ></u-search>
        </view>
        <view class="tabs-box">
            <u-tabs 
                :list="tabList"
                :scrollable="false"
                lineWidth="30"
                lineHeight="5"
                lineColor="#F15353"
                :activeStyle="{
                    color: '#F15353',
                    fontWeight: 'bold',
                }"
                :inactiveStyle="{
                    color: '#606266',
                }"
                itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
                @click="tabsTap"
            >
            </u-tabs>
         </view>
         <template v-for="(item,index) in list">
            <view class="main-box mt_20" :key="item.id">
               <view class="f fac fjsb"  @click="goPaymentDetail(item.id) ">
                   <view class="f fac">
                       <view v-if="tabName === '未打款记录'">
                           <u-checkbox-group placement="column">
                               <u-checkbox
                                    activeColor="#F15353" 
                                    size="30" 
                                    shape="circle"
                                    :name="item.id"
                                    :checked="item.checked"
                                    
                                    @change='changeChecked(item,index)'
                                ></u-checkbox>
                                <!-- :disabled="item.award_status === 0" -->
                           </u-checkbox-group>
                       </view>
                       <view class="avatar-box">
                           <u--image
                               :src="item.receive_user.avatar"
                               shape="circle"
                               width="80rpx"
                               height="80rpx"
                           ></u--image>
                       </view>
                       <view class="ml_20">
                           <view class="black fwb">{{ item.receive_user.nickname }}</view>
                           <view class="grey">ID：{{ item.receive_uid }}</view>
                       </view>
                   </view>
                   <view>
                       <view class="red" v-if="tabName === '未打款记录'">未打款</view>
                       <view class="green" v-if="tabName === '已打款记录'">已打款</view>
                   </view>
               </view>
               <view>
                   <view class="f fac fjsb pt_30">
                   <view class="black">订单号：{{ item.order_sn }}</view>
                   <view class="black">￥{{ toYuan(item.amount) }}</view>
                   </view>
                   <view class="f fac fjsb mt_20 grey">
                       <view>{{ formatDateTime(item.created_at) }}</view>
                   </view>
               </view>
            </view>
         </template>

        <view class="button-box f fac fjsb" v-if="tabName === '未打款记录'">
            <view class="f fac ml_30">
                <u-checkbox-group v-model="allCheckbox">
                    <u-checkbox 
                        activeColor="#F15353" 
                        :checked="allFlag == 0 ? false : true" 
                        size="32" 
                        shape="circle" 
                        @change="checkAll"
                    ></u-checkbox>
                </u-checkbox-group>
                <view>
                    <text class="black">全选</text>
                    <text class="red">￥{{ toYuan(amount_total) }}</text>
                </view>
            </view>
            <view class="f fac mr_25" style="width: 180rpx">
                <u-button style="width: 180rpx !important;" shape="circle" color="#F14E4E" text="打款" @click="remit"></u-button>
            </view>
         </view>
    </view>
</template>
<script>
export default {
    name: 'levelPayment',
    data() {
        return {
            user_kwd: '',
            checked:[],
            allFlag: 0,
            allCheckbox: [], // 全选
            tabList: [
                {
                    name: '未打款记录',
                }, 
                {
                    name: '已打款记录',
                }
            ],
            tabName: '未打款记录',
            page: 1,
            pageSize: 10,
            total: 0,
            list: [],
            amount_total: 0
        }
    },
    onLoad() {
        
    },
    onShow() {
        this.page = 1;
        this.list = [];
        this.allFlag = 0;
        this.user_kwd = '';
        this.tabName = '未打款记录';
        this.total = 0;
        this.getList();
    },
    // 下拉刷新
    onReachBottom() {
        if (this.list.length < this.total) {
            this.page = this.page + 1;
            this.getList();
        }
    },
    methods: {
        // 搜索
        search() {
            this.page = 1;
            this.list = [];
            this.total = 0;
            this.getList();
        },
        // 切换tabs
        tabsTap(item) {
            this.tabName = item.name;
            this.page = 1;
            this.total = 0;
            this.list = [];
            this.getList();
        },
        // 获取数据
        async getList() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
            };
            if (this.tabName === '未打款记录') {
                params.payment_status = 0
            } else {
                params.payment_status = 1
            };
            if (this.user_kwd !== '') {
                params.user_kwd = this.user_kwd;
            }
            let res = await this.get('/api/agent/reward/pay/list',params);
            if (res.code === 0) {
                let list = [];
                res.data.list.forEach(e => {
                    list.push({...e,checked: false});
                });
                this.list = [...this.list,...list];
                this.total = res.data.total;
                this.allFlagShow();
            }
        },
        // 勾选平级奖
        changeChecked(row,index) {
            this.list[index].checked = !row.checked;
            if (this.list[index].checked) {
                this.amount_total += this.list[index].amount;
            } else {
                this.amount_total -= this.list[index].amount;
            }          
            this.allFlagShow();
        },
        // 判断当前是否全选
        allFlagShow() {
            let show = false
            this.list.forEach(item => {
                if (item.checked === false) {
                    show = true;
                    return;
                }
            })
            this.allFlag = show ? 0 : 1;
        },
        // 全选功能
        checkAll() {
            //全选功能
            this.allFlag = !this.allFlag;
            if (this.allFlag) {
                let num = 0;
                this.list.forEach(item => { 
                    item.checked = true;
                    num += item.amount
                })
                this.amount_total = num;
            } else {
                this.list.forEach(item => { item.checked = false;})
                this.amount_total = 0;
            }
        },
        // 打款
        remit() {
            let arr = this.list.filter(item => item.checked);
            if (arr.length > 0) {
                let ids = [arr[0].id];
                let user = arr[0].receive_uid
                let show = false;
                for (let i = 1; i < arr.length; i++) {
                    const element = arr[i];
                    ids.push(element.id)
                    if (element.receive_uid !== user) {
                        show = true;
                        break;
                    }
                }
                
                if (show) {
                    this.showText('请选择同一个会员进行打款操作');
                    return
                }
                this.navTo('/packageE/agent/paymentDetail?ids=' + ids)
            } else {
                this.showText('需勾选未打款的记录');
            }
        },
        goPaymentDetail(id) {
            if (this.tabName === '未打款记录') {
                this.navTo('/packageE/agent/paymentDetail?ids=' + id);
            } 
        }
    }
}
</script>
<style lang="scss" scoped>
.search-box {
    width: 750rpx;
    height: 96rpx;
    background: #FFFFFF;
    padding: 16rpx 24rpx;
    box-sizing: border-box;
}
.tabs-box {
    width: 750rpx;
    height: 78rpx;
    background: #ffffff;
}
.main-box {
    width: 702rpx;
    height: 254rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-left: 24rpx;
    padding: 22rpx 24rpx 31rpx 29rpx;
    box-sizing: border-box;
}
.button-box {
    width: 750rpx;
    height: 98rpx;
    background: #FFFFFF;
    position: fixed;
    bottom: 0rpx;
}
.grey {
    font-weight: 400;
    font-size: 26rpx;
    color: #919191;
}
.black {
    font-weight: 500;
    font-size: 26rpx;
    color: #202020
}
.red {
    font-weight: 500;
    font-size: 28rpx;
    color: #F15353;
}
.green {
    font-weight: 500;
    font-size: 28rpx;
    color: #19BE76;
}
.fwb {
    font-weight: bold;
}
::v-deep .u-button--info {
    width: 180rpx !important;
    height: 72rpx;
    color: #f14e4e;
    border-color: #f14e4e;
}
</style>