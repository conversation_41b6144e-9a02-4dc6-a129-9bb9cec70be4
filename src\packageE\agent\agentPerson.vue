<template>
  <view>
    <view class="agent-box">
      <u-form :model="fromData" ref="fromData">
        <u-form-item label="申请等级" labelWidth="170rpx" borderBottom>
          <u--input
            v-model="fromData.applyLevel"
            :disabled="true"
            placeholder=""
            border="none"
          ></u--input>
        </u-form-item>
        <u-form-item label="姓名" labelWidth="170rpx" borderBottom>
          <u--input
            v-model="fromData.username"
            :disabled="true"
            placeholder=""
            border="none"
          ></u--input>
        </u-form-item>
        <u-form-item label="电话" labelWidth="170rpx" borderBottom>
          <u--input
            v-model="fromData.mobile"
            :disabled="true"
            placeholder=""
            border="none"
          ></u--input>
        </u-form-item>
        <u-form-item label="用户地址" labelWidth="170rpx" borderBottom>
          <u--input
            v-model="fromData.city"
            :disabled="true"
            placeholder=""
            border="none"
          ></u--input>
        </u-form-item>
        <u-form-item label="街道" labelWidth="170rpx" borderBottom>
          <u--input
            v-model="fromData.street"
            :disabled="true"
            placeholder=""
            border="none"
          ></u--input>
        </u-form-item>
        <u-form-item label="详细地址" labelWidth="170rpx" borderBottom>
          <u--input
            v-model="fromData.address"
            :disabled="true"
            placeholder=""
            border="none"
          ></u--input>
        </u-form-item>
        <u-form-item label="微信二维码" labelWidth="170rpx"></u-form-item>
        <u--image
          :src="fromData.wechat_qr_code"
          width="204rpx"
          height="204rpx"
        ></u--image>
        <u-form-item label="支付宝收款码" labelWidth="190rpx"></u-form-item>
        <u--image
          :src="fromData.alipay_pay_code"
          width="204rpx"
          height="204rpx"
        ></u--image>
        <u-form-item borderBottom></u-form-item>
        <u-form-item label="银行卡收款账号" labelWidth="170rpx" borderBottom>
          <u--input
            v-model="fromData.bank_account"
            :disabled="true"
            placeholder=""
            border="none"
          ></u--input>
        </u-form-item>
        <u-form-item label="微信收款码" labelWidth="170rpx"></u-form-item>
        <u--image
          :src="fromData.wechat_pay_code"
          width="204rpx"
          height="204rpx"
        ></u--image>
        <view class="form-box mt_20" v-for="item in fields_data" :key="item.id">
          <u-form-item :label="item.label" labelWidth="170rpx" borderBottom>
            <u--input
              v-if="item.type === 'text'"
              border="none"
              :disabled="true"
              v-model="item.value"
            ></u--input>
            <view class="ml_5 f fac" v-if="item.type === 'images'">
              <u--image
                v-for="items in item.value"
                :key="items.url"
                :src="items.url"
                width="160rpx"
                height="160rpx"
              ></u--image>
            </view>
          </u-form-item>
        </view>
        <view class="mt_40 pb_150"></view>
      </u-form>
    </view>
  </view>
</template>

<script>
export default {
  name: 'agentPerson',
  data() {
    return {
      fromData: {
        applyLevel: '', // 等级
        username: '', // 姓名
        mobile: '', // 电话
        city: '', // 地址
        street: '', // 街道
        address: '', // 详细地址
        bank_account: '', // 银行卡
        wechat_qr_code: '', // 微信二维码
        alipay_pay_code: '', // 支付宝收款码
        wechat_pay_code: '', // 微信收款码
      },
      fields_data: [], // 自定义字段
    }
  },
  onLoad() {
    let info = uni.getStorageSync('agent_info')
    this.fromData = info
    this.fromData.applyLevel = info.agent_level.level_name
    this.fromData.street = info.street.name
    this.fromData.city =
      info.province.name + info.city.name + info.district.name
    this.fields_data = info.fields_data ? JSON.parse(info.fields_data) : []
  },
  destroyed() {
    uni.removeStorageSync('agent_info')
  },
}
</script>

<style lang="scss" scoped>
.bohui-box {
  width: 750rpx !important;
  height: 190rpx;
  background: linear-gradient(180deg, #f14e4e 0%, #f5f5f5 100%);
  box-sizing: border-box;
}
.bohui-title {
  font-weight: bold;
  font-size: 40rpx;
  color: #ffffff;
}
.shenhe-box {
  width: 750rpx !important;
  height: 190rpx;
  background: linear-gradient(180deg, #ff8a14 0%, #f5f5f5 100%);
  box-sizing: border-box;
}
.agent-box {
  width: 702rpx;
  margin-top: 20rpx;
  margin-left: 24rpx;
  margin-right: 24rpx;
  border-radius: 16rpx;
}
.form-box {
  background: #ffffff;
  border-radius: 16rpx;
  width: 702rpx;
  padding: 14rpx 32rpx;
  box-sizing: border-box;
}
.w100 {
  width: 100%;
}
.input-view-name {
  font-size: 30rpx;
}
.input-view-name1 {
  font-size: 30rpx;
  color: rgb(192, 196, 204);
}
.tip {
  color: #f14e4e;
  font-size: 26rpx;
}
.black {
  color: #202020;
  font-size: 26rpx;
}
.title {
  font-weight: bold;
  font-size: 28rpx;
}
.fs_30 {
  font-size: 30rpx;
}
.-mt_60 {
  margin-top: -60rpx;
}
::v-deep .u-radio__icon-wrap--circle {
  width: 32rpx !important;
  height: 32rpx !important;
}
::v-deep .u-radio-group--row {
  flex-direction: column !important;
}
::v-deep .u-radio__text {
  color: #00001c !important;
  font-size: 28rpx !important;
}
</style>
