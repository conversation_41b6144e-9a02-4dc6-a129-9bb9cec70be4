<template>
  <view>
    <view class="tabs-box">
      <u-tabs
        :list="list"
        :current="current"
        @change="changeType"
        :scrollable="false"
        lineWidth="30"
        lineHeight="5"
        lineColor="#F15353"
        :activeStyle="{
          color: '#F15353',
          fontWeight: 'bold',
        }"
        :inactiveStyle="{
          color: '#606266',
        }"
        itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
      ></u-tabs>
    </view>
    <view v-if="current === 0">
      <view class="recharge-box mt_20">
        <view class="">余额</view>
        <view class="f fac fjsb mt_20">
          <view class="black">{{ toYuan(balance) }}</view>
          <view style="width: 136rpx">
            <u-button
              style="width: 136rpx !important; height: 64rpx !important;"
              shape="circle"
              color="#F15353"
              text="充值"
              @click="amountBalance"
            ></u-button>
          </view>
        </view>
        <view class="grey mt_20">注：充值余额到账为上级代理商账户！</view>
      </view>
      <view class="agent-box f fac mt_20">
        <view class="agent-title">上级代理商：</view>
        <view v-if="is_agent" class="f fac">
          <u--image
            :src="parent_agent_user.avatar"
            shape="circle"
            width="56rpx"
            height="56rpx"
          ></u--image>
          <view class="agent-title">
            【{{ parent_agent_user.id }}】{{ parent_agent_user.nickname }}
          </view>
        </view>
        <view v-else class="agent-title">平台</view>
      </view>
      <view class="tab-box mt_20">
        <u-tabs
          :list="list1"
          :current="currentPriceType"
          :scrollable="false"
          lineWidth="30"
          lineHeight="5"
          lineColor="#F15353"
          :activeStyle="{
            color: '#F15353',
            fontWeight: 'bold',
          }"
          :inactiveStyle="{
            color: '#606266',
          }"
          itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
          @change="changePriceType"
        ></u-tabs>
      </view>
      <view class="main-box mt_20" v-for="item in balanceList" :key="item.id">
        <view class="f fac fjsb">
          <view class="vip-title" v-if="item.business_type === 1">
            订单采购
          </view>
          <view class="vip-title" v-if="item.business_type === 2">
            会员充值
          </view>
          <view class="vip-title" v-if="item.business_type === 3">
            手动充值
          </view>
          <view class="red" v-if="item.price_type === 1">
            +{{ toYuan(item.price) }}
          </view>
          <view class="green" v-if="item.price_type === 0">
            -{{ toYuan(item.price) }}
          </view>
        </view>
        <view class="f fac fjsb mt_15">
          <view class="vip-name">操作人：{{ item.user.nickname }}</view>
          <view class="vip-name">剩余余额：{{ toYuan(item.balance) }}</view>
        </view>
        <view class="grey mt_15">{{ formatDateTime(item.created_at) }}</view>
      </view>
      <view class="mb_100"></view>
    </view>
    <view v-if="current === 1">
      <view class="recharge-boxs mt_20">
        <view class="">总余额</view>
        <view class="f fac fjsb mt_20">
          <view class="black">{{ toYuan(totalAmount) }}</view>
        </view>
      </view>
      <view class="tab-box mt_20">
        <u-tabs
          :list="list2"
          :current="balanceCurrent"
          @change="changebalanceType"
          :scrollable="false"
          lineWidth="30"
          lineHeight="5"
          lineColor="#F15353"
          :activeStyle="{
            color: '#F15353',
            fontWeight: 'bold',
          }"
          :inactiveStyle="{
            color: '#606266',
          }"
          itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
        ></u-tabs>
      </view>
      <view class="search-box mt_20">
        <u-search
          height="64rpx"
          placeholder="搜索会员ID/昵称/手机号"
          clearabled
          :showAction="false"
          searchIconSize="40"
          bgColor="#ffffff"
          v-model="searchMember"
          @search="changeSearchMember"
        ></u-search>
      </view>
      <view
        v-if="balanceCurrent === 0"
        class="main-boxs mt_20"
        v-for="item in memberList"
        :key="item.id"
      >
        <view class="f fac">
          <u--image
            :src="item.user.avatar"
            shape="circle"
            width="80rpx"
            height="80rpx"
          ></u--image>
          <view class="ml_15">
            <view class="nick-title">{{ item.user.nickname }}</view>
            <view class="grey-title">ID：{{ item.user.id }}</view>
          </view>
        </view>
        <view class="grey-title mt_35">
          推荐人：{{ item.user.parent_user.nickname }}（{{
            item.user.parent_user.id
          }}）
        </view>
        <view class="grey-title mt_15">
          余额：{{ toYuan(item.agent_balance.balance) }}
        </view>
        <view class="grey-title mt_15">
          注册时间：{{ formatDateTime(item.created_at) }}
        </view>
        <view class="f fac fjsb mt_30">
          <view></view>
          <view class="f fac mr_20">
            <view class="mr_20">
              <u-button
                class="mr_15"
                :plain="true" 
                type="error"
                shape="circle"
                text="余额明细"
                @click="balanceDetail(item)"
              ></u-button>
            </view>
            <view>
              <u-button
                v-if="manual_recharge_switch"
                style="width: 180rpx"
                shape="circle"
                color="#F14E4E"
                text="充值余额"
                @click="memberBalance(item)"
              ></u-button>
            </view>
          </view>
        </view>
      </view>
      <view v-if="balanceCurrent === 1">
        <view class="mt_20">
          <u-tabs
            :list="list3"
            :current="customerBalanceCurrent"
            :scrollable="false"
            lineWidth="30"
            lineHeight="5"
            lineColor="#F15353"
            :activeStyle="{
              color: '#F15353',
              fontWeight: 'bold',
            }"
            :inactiveStyle="{
              color: '#606266',
            }"
            itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
            @change="changeCustomerBalance"
          ></u-tabs>
        </view>
        <view
          class="mt_20 member-box"
          v-for="item in customerBalanceList"
          :key="item.id"
        >
          <view class="f fac fjsb">
            <view class="agent-title fwb">会员充值</view>
            <view class="grey-title">
              {{ formatDateTime(item.created_at) }}
            </view>
          </view>
          <view class="f fac fjsb mt_35">
            <view class="f fac">
              <u--image
                :src="item.user.avatar"
                shape="circle"
                width="48rpx"
                height="48rpx"
              ></u--image>
              <view class="grey-title">
                {{ item.user.nickname }}({{ item.user.id }})
              </view>
            </view>
            <view class="grey-title">{{ toYuan(item.price) }}</view>
          </view>
          <view class="grey-title mt_20">余额：{{ toYuan(item.balance) }}</view>
          <view class="grey-title mt_20" v-if="item.remark">
            备注：{{ item.remark }}
          </view>
        </view>
        <view class="mb_100"></view>
      </view>
    </view>
    <u-popup :show="show" mode="bottom" round="30rpx" @close="show = false">
      <view class="popup-box">
        <view class="f fac fjsb">
          <view class="vip-title">充值余额</view>
          <view
            class="iconfont icon-adsystem_icon_cancle icon"
            @click="show = false"
          ></view>
        </view>
        <view class="popup-rechage-box mt_40">
          <view class="f fac">
            <u--image
              :src="popup_detail.avatar"
              shape="circle"
              width="48rpx"
              height="48rpx"
            ></u--image>
            <view class="agent-title fs30 ml_10">
              {{ popup_detail.nickname }}
            </view>
          </view>
          <view class="f fac mt_50">
            <view>当前余额</view>
            <view class="black ml_10">{{ toYuan(popup_detail.balance) }}</view>
          </view>
        </view>
        <view class="rechange-number-box f fac mt_20">
          <view>
            <text class="red-icon">*</text>
            充值数量
          </view>
          <u--input
            v-model="agentAmount"
            class="ml_50"
            placeholder="请输入充值数量"
            border="none"
          ></u--input>
        </view>
        <view class="textarea-box f mt_20">
          <view class="mt_20">备注</view>
          <u--textarea
            v-model="remarks"
            class="ml_90"
            placeholder="请输入备注"
            border="none"
          ></u--textarea>
        </view>
        <u-button
          class="mt_50"
          style="width: 702rpx"
          shape="circle"
          color="#F14E4E"
          text="确定"
          @click="agentRecharge"
        ></u-button>
      </view>
    </u-popup>
  </view>
</template>
<script>
export default {
  name: 'balancePurchase',
  data() {
    return {
      show: false,
      current: 0,
      balanceCurrent: 0,
      balance: 0, // 我的余额
      price_type: null, // 我的余额收入或支出
      currentPriceType: 0, // 我的余额收入或支出
      remarks: '', // 备注
      agentAmount: null, // 充值余额
      is_agent: false, // 是否显示上级代理商
      parent_agent_user: {
        avatar: '',
        id: '',
        nickname: '',
      }, // 上级代理商信息
      user: {
        avatar: '',
        nickname: '',
      }, // 会员呢信息
      popup_detail: {
        avatar: '',
        nickname: '',
        balance: 0,
      },
      searchMember: '', // 搜索会员信息
      user_id: '', // 会员ID
      list: [
        {
          name: '我的余额',
          index: 0,
        },
        {
          name: '客户余额',
          index: 1,
        },
      ],
      list1: [
        {
          name: '全部',
        },
        {
          name: '收入',
        },
        {
          name: '支出',
        },
      ],
      list2: [
        {
          name: '会员列表',
          index: 0,
        },
        {
          name: '余额明细',
          index: 1,
        },
      ],
      list3: [
        {
          name: '全部',
        },
        {
          name: '收入',
        },
        {
          name: '支出',
        },
      ],
      balanceList: [],
      balancePage: 1, // 我的余额页
      balancePageSize: 10, // 我的余额每页条数
      balanceTotal: null, // 我的余额总数
      totalAmount: null, // 客户总余额
      memberList: [], // 会员列表
      memberPage: 1, // 会员余额页
      memberPageSize: 10, // 会员余额每页条数
      memberTotal: 0, // 会员余额总数
      customerBalanceCurrent: 0, // 客户余额下标
      customerBalanceList: [], // 客户余额明细列表
      customerBalancePage: 1, // 客户余额页
      customerBalancePageSize: 10, // 客户余额每页条数
      customerBalanceTotal: null, // 客户余额总数
      balanceDetailId: null,
      manual_recharge_switch: 0, // 控制手动充值
    }
  },
  onLoad(options) {
    this.user = uni.getStorageSync('user')
  },
  onShow() {
    this.getAgentBalanceByUid()
    this.getAgentBalanceDetailList()
    this.getClientAgentBalance()
    this.agentListByAgentUserId()
    this.agentBalanceDetailApplicationList()
    this.getIsAgentPay()
    this.getAgentSetting()
  },
  async onReachBottom() {
    if (this.balanceList.length < this.balanceTotal && this.current === 0) {
      this.balancePage = this.balancePage + 1
      const data = {
        price_type: this.price_type,
        page: this.balancePage,
        page_size: this.balancePageSize,
      }
      const res = await this.post(
        '/api/agent/Balance/getAgentBalanceDetailList',
        data,
      )
      if (res.code === 0) {
        this.balanceList = this.balanceList.concat(res.data.list)
      }
    }
    if (this.memberList.length < this.memberTotal && this.current === 1) {
      this.memberPage = this.memberPage + 1
      const data = {
        keyword: this.searchMember,
        page: this.memberPage,
        pageSize: this.memberPageSize,
      }
      const res = await this.post(
        '/api/agent/Balance/agentListByAgentUserId',
        data,
      )
      if (res.code === 0) {
        this.memberList = this.memberList.concat(res.data.list)
      }
    }
    if (
      this.customerBalanceList.length < this.customerBalanceTotal &&
      this.current === 1
    ) {
      this.customerBalancePage = this.customerBalancePage + 1
      const data = {
        price_type: this.price_type,
        uid: parseInt(this.balanceDetailId),
        page: this.customerBalancePage,
        page_size: this.customerBalancePageSize,
      }
      const res = await this.post(
        '/api/agent/Balance/agentBalanceDetailApplicationList',
        data,
      )
      if (res.code === 0) {
        this.customerBalanceList = this.customerBalanceList.concat(
          res.data.list,
        )
      }
    }
  },
  methods: {
    // 获取代理商基础配置
    async getAgentSetting() {
      const res = await this.get('/api/agent/setting/get')
      if (res.code === 0) {
        console.log('wqewqeqweq', res)
        this.manual_recharge_switch =
          res.data.setting.value.base_setting.manual_recharge_switch
      }
    },
    // 是否可以使用代理商支付 -- 同时返回上级代理商信息以及代理商的用户信息
    async getIsAgentPay() {
      const res = await this.post(
        '/api/agent/Balance/getIsAgentPay',
        {},
        false,
        false,
        false,
      )
      if (res.code === 0) {
        this.is_agent = res.data.is_agent
        this.parent_agent_user = res.data.parent_agent_user
        this.parent_agent_user.nickname = res.data.parent_agent.username
      }
    },
    // 获取我的余额
    async getAgentBalanceByUid() {
      const res = await this.post('/api/agent/Balance/getAgentBalanceByUid')
      if (res.code === 0) {
        this.balance = res.data.balance
      }
    },
    // 代理商余额充值
    async agentRecharge() {
      if (!this.agentAmount) {
        this.toast('请输入充值数量')
        return
      }
      const data = {
        uid:
          this.current === 1 ? parseInt(this.user_id) : parseInt(this.user.id),
        amount: this.agentAmount * 100,
        remarks: this.remarks,
      }
      const res = await this.post('/api/agent/Balance/agentRecharge', data)
      if (res.code === 0) {
        this.current === 1
          ? this.getClientAgentBalance()
          : this.getAgentBalanceByUid()
        this.getAgentBalanceDetailList()
        this.agentBalanceDetailApplicationList()
        this.agentListByAgentUserId()
        this.toast(res.data)
        this.agentAmount = null
        this.remarks = ''
        this.show = false
      }
    },
    // 我的余额明细
    async getAgentBalanceDetailList() {
      const data = {
        price_type: this.price_type,
        page: this.balancePage,
        page_size: this.balancePageSize,
      }
      const res = await this.post(
        '/api/agent/Balance/getAgentBalanceDetailList',
        data,
      )
      if (res.code === 0) {
        this.balanceList = res.data.list
        this.balanceTotal = res.data.total
      }
    },
    // 修改我的余额收入或支出
    changePriceType(item) {
      this.balancePage = 1
      switch (item.index) {
        case 0:
          this.price_type = null
          this.currentPriceType = 0
          this.getAgentBalanceDetailList()
          break
        case 1:
          this.price_type = 1
          this.currentPriceType = 1
          this.getAgentBalanceDetailList()
          break
        case 2:
          this.price_type = 0
          this.currentPriceType = 2
          this.getAgentBalanceDetailList()
          break
        default:
          break
      }
    },
    // 我的余额充值
    amountBalance() {
      this.navTo('/packageB/member/recharge?pay_type=2&agent=true')
      // this.show = true
      // this.popup_detail.avatar = this.user.avatar
      // this.popup_detail.nickname = this.user.nickname
      // this.popup_detail.balance = this.balance
    },
    // 获取客户总余额
    async getClientAgentBalance() {
      const res = await this.post('/api/agent/Balance/getClientAgentBalance')
      if (res.code === 0) {
        this.totalAmount = res.data.total
      }
    },
    // 会员列表搜索
    changeSearchMember() {
      this.balanceCurrent === 1
        ? this.agentBalanceDetailApplicationList()
        : this.agentListByAgentUserId()
    },
    // 获取会员列表
    async agentListByAgentUserId() {
      const data = {
        keyword: this.searchMember,
        page: this.memberPage,
        pageSize: this.memberPageSize,
      }
      const res = await this.post(
        '/api/agent/Balance/agentListByAgentUserId',
        data,
      )
      if (res.code === 0) {
        this.memberList = res.data.list
        this.memberTotal = res.data.total
      }
    },
    // 余额明细
    balanceDetail(item) {
      this.balanceCurrent = 1
      this.balanceDetailId = item.user_id
      this.customerBalancePage = 1
      this.agentBalanceDetailApplicationList()
    },
    // 会员充值
    memberBalance(item) {
      this.show = true
      this.user_id = item.user.id
      this.popup_detail.avatar = item.user.avatar
      this.popup_detail.nickname = item.user.nickname
      this.popup_detail.balance = item.agent_balance.balance
    },
    // 客户余额明细
    async agentBalanceDetailApplicationList() {
      const data = {
        price_type: this.price_type,
        uid: parseInt(this.balanceDetailId),
        keyword: this.searchMember,
        page: this.customerBalancePage,
        page_size: this.customerBalancePageSize,
      }
      const res = await this.post(
        '/api/agent/Balance/agentBalanceDetailApplicationList',
        data,
      )
      if (res.code === 0) {
        this.customerBalanceList = res.data.list
        this.customerBalanceTotal = res.data.total
      }
    },
    // 客户余额切换
    changeCustomerBalance(item) {
      this.customerBalancePage = 1
      switch (item.index) {
        case 0:
          this.price_type = null
          this.customerBalanceCurrent = 0
          this.agentBalanceDetailApplicationList()
          break
        case 1:
          this.price_type = 1
          this.customerBalanceCurrent = 1
          this.agentBalanceDetailApplicationList()
          break
        case 2:
          this.price_type = 0
          this.customerBalanceCurrent = 2
          this.agentBalanceDetailApplicationList()
          break
        default:
          break
      }
    },
    changeType(item) {
      this.current = item.index
    },
    changebalanceType(item) {
      this.balanceCurrent = item.index
    },
  },
}
</script>
<style lang="scss" scoped>
.tabs-box {
  width: 750rpx;
  height: 78rpx;
  background: #ffffff;
}
.tab-box {
  width: 750rpx;
  height: 78rpx;
}
.recharge-box {
  width: 702rpx;
  height: 233rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-left: 24rpx;
  padding: 42rpx 24rpx 36rpx 24rpx;
  box-sizing: border-box;
}
.recharge-boxs {
  width: 702rpx;
  height: 183rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-left: 24rpx;
  padding: 42rpx 24rpx 36rpx 24rpx;
  box-sizing: border-box;
}
.main-box {
  width: 702rpx;
  height: 172rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-left: 24rpx;
  padding: 30rpx 24rpx;
  box-sizing: border-box;
}
.search-box {
  width: 702rpx;
  margin-left: 24rpx;
}
.main-boxs {
  width: 702rpx;
  height: 379rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-left: 24rpx;
  padding: 22rpx 19rpx 24rpx 24rpx;
  box-sizing: border-box;
}
.grey-title {
  font-size: 26rpx;
  color: #818181;
}
.black {
  font-weight: bold;
  font-size: 56rpx;
  color: #00001c;
}
.grey {
  font-weight: normal;
  font-size: 24rpx;
  color: #919191;
}
.agent-title {
  font-size: 28rpx;
  color: #202020;
}
.vip-title {
  font-weight: bold;
  font-size: 30rpx;
  color: #202020;
}
.vip-name {
  font-weight: 400;
  font-size: 26rpx;
  color: #202020;
}
.nick-title {
  font-weight: bold;
  font-size: 30rpx;
  color: #00001c;
}
.red {
  font-weight: 500;
  font-size: 28rpx;
  color: #f15353;
}
.green {
  font-weight: 500;
  font-size: 28rpx;
  color: #19be76;
}
.icon {
  font-size: 40rpx;
  color: #6e6e79;
}
.red-icon {
  color: #f15353;
}
.agent-box {
  width: 702rpx;
  height: 96rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-left: 24rpx;
  padding-top: 34rpx;
  padding-bottom: 34rpx;
  padding-left: 24rpx;
  box-sizing: border-box;
}
.popup-box {
  width: 750rpx;
  height: 954rpx;
  background: #f5f5f5;
  border-radius: 30rpx 30rpx 0rpx 0rpx;
  padding: 48rpx 24rpx 46rpx 24rpx;
  box-sizing: border-box;
  .popup-rechage-box {
    width: 702rpx;
    height: 217rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx 24rpx;
    box-sizing: border-box;
  }
  .rechange-number-box {
    width: 702rpx;
    height: 104rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 38rpx 24rpx;
    box-sizing: border-box;
  }
  .textarea-box {
    width: 702rpx;
    height: 297rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 38rpx 24rpx;
    box-sizing: border-box;
  }
}
.member-box {
  width: 702rpx;
  height: 271rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-left: 24rpx;
  padding: 28rpx 23rpx;
  box-sizing: border-box;
}
.fs30 {
  font-size: 30rpx;
}
.fwb {
  font-weight: bold;
}
::v-deep .u-button--info {
  width: 180rpx;
  height: 72rpx;
  color: #f14e4e;
  border-color: #f14e4e;
}
::v-deep .uni-input-input {
  text-align: left !important;
}
</style>
